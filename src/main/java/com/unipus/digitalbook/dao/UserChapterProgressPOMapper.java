package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.action.UserChapterProgressPO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【user_chapter_progress(用户章节进度表)】的数据库操作Mapper
* @createDate 2025-06-09 11:10:46
* @Entity com.unipus.digitalbook.model.po.action.UserChapterProgressPO
*/
public interface UserChapterProgressPOMapper {

    int deleteByPrimaryKey(Long id);

    int insert(UserChapterProgressPO record);

    int insertSelective(UserChapterProgressPO record);

    UserChapterProgressPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserChapterProgressPO record);

    int updateByPrimaryKey(UserChapterProgressPO record);

    void saveProgressBit(Long tenantId, String openId, String chapterId, Long chapterVersionId, byte[] progressBit);

    byte[] getProgressBit(Long tenantId, String openId, Long chapterVersionId);

    void clearProgressBit(Long tenantId, String openId, Long chapterVersionId);

    List<UserChapterProgressPO> getProgressList(Long tenantId, Long chapterVersionId, Long lastId);
}
