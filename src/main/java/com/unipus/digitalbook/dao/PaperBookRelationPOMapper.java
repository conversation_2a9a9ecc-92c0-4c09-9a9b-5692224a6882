package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.paper.PaperBookRelationPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PaperBookRelationPOMapper {

    /**
     * 插入题目引用
     * @param paperBookRelationPO 试卷引用对象
     * @return 插入的行数
     */
    int insertOrUpdate(PaperBookRelationPO paperBookRelationPO);

    /**
     * 更新题目引用
     * @param paperBookRelationPO 试卷引用对象
     * @return 更新的行数
     */
    int update(PaperBookRelationPO paperBookRelationPO);

    /**
     * 获取教材的所有试卷
     * @param bookId 教材ID
     * @return 题目引用列表
     */
    List<PaperBookRelationPO> selectList(@Param("bookId")String bookId);

    /**
     * 获取试卷关联的教材ID
     * @param paperId 试卷ID
     * @return 教材ID
     */
    String selectBookIdByPaperId(@Param("paperId")String paperId);

}