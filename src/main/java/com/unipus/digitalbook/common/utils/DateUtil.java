package com.unipus.digitalbook.common.utils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 时间日期工具类
 */
public class DateUtil {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter FORMATTER_DATE_TIME =  DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
    private static final DateTimeFormatter FORMATTER_DATE_TIME_CURRENT_YEAR =  DateTimeFormatter.ofPattern("MM-dd HH:mm");
    public static final DateTimeFormatter FORMATTER_DAY = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public static Date max(Date date1, Date date2) {
        if (date1 == null) {
            return date2;
        }
        if (date2 == null) {
            return date1;
        }
        return date1.compareTo(date2) > 0 ? date1 : date2;
    }

    public static String dateTimeFormat(Date date) {
        return FORMATTER.format(LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()));
    }

    public static String dateTimeFormatMinutes(Date date) {
        return FORMATTER_DATE_TIME.format(LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()));
    }

    public static Integer getYear(Date date) {
    	LocalDateTime localDateTime = LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    	return localDateTime.getYear();
    }

    public static String dateTimeFormat(Date date, String format) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
        return dateTimeFormatter.format(LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()));
    }

    public static String dateTimeFormatYMD(Long epochSeconds) {
        LocalDate date = Instant.ofEpochSecond(epochSeconds).atZone(ZoneId.systemDefault()).toLocalDate();
        return FORMATTER_DAY.format(date);
    }

    /**
     * 格式化日期， 当前年份：MM-dd HH:mm, 其他年份：yyyy-MM-dd HH:mm
     */
    public static String formatDateTimeMinutesWithYearOrNot(Date date) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
        LocalDateTime now = LocalDateTime.now();

        if (localDateTime.getYear() == now.getYear()) {
            return FORMATTER_DATE_TIME_CURRENT_YEAR.format(localDateTime);
        }else {
            return FORMATTER_DATE_TIME.format(localDateTime);
        }
    }
}
