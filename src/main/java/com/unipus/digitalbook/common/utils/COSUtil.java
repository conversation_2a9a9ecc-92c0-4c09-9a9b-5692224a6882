package com.unipus.digitalbook.common.utils;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.*;
import com.qcloud.cos.utils.IOUtils;
import com.qcloud.cos.utils.Jackson;
import com.qcloud.cos.utils.Md5Utils;
import com.qcloud.cos.utils.StringUtils;
import com.tencent.cloud.CosStsClient;
import com.tencent.cloud.Policy;
import com.tencent.cloud.Response;
import com.tencent.cloud.Statement;
import com.unipus.digitalbook.common.exception.cos.COSCredentialException;
import com.unipus.digitalbook.common.exception.cos.COSException;
import com.unipus.digitalbook.conf.cos.COSProperties;
import com.unipus.digitalbook.model.entity.cos.COSCredential;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.TreeMap;
import java.util.UUID;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/11 12:29
 */
@Component
@Slf4j
public class COSUtil {

    @Resource
    private COSProperties cosProperties;
    @Resource
    private COSClient cosClient;

    /**
     * 根据指定的键获取对象的URL
     *
     * @param key 对象的唯一标识符
     * @return 对象的URL地址
     */
    public String getUrlByKey(String key) {
        return URLDecoder.decode(cosClient.getObjectUrl(cosProperties.getBucket(), key).toString(), StandardCharsets.UTF_8);
    }

    /**
     * 获取预签名URL
     *
     * @param key            对象的键，即对象在存储桶中的唯一标识符
     * @param expirationDate 签名的过期时间，指定到具体的日期和时间点
     * @return 返回生成的预签名URL字符串
     */
    public String getPresignedUrlByKey(String key, Date expirationDate) {
        // 设置签名过期时间(可选), 若未进行设置, 则默认使用 ClientConfig 中的签名过期时间(1小时)
        return cosClient.generatePresignedUrl(cosProperties.getBucket(), key, expirationDate).toString();
    }

    public String getPresignedUrlByUrl(String url, Date expirationDate) {
        String objectKey = getObjectKeyByUrl(url);
        return getPresignedUrlByKey(objectKey, expirationDate == null ? new Date(System.currentTimeMillis() + 60 * 1000) : expirationDate);
    }

    /**
     * 上传文件并获取其URL地址
     *
     * @param fileName 文件名，用于指定要上传的文件名称
     * @param bytes    文件内容的字节数组，包含要上传的文件的数据
     * @return 返回上传文件的URL地址，通过这个URL可以访问到上传的文件
     */
    public String getUploadUrl(String fileName, byte[] bytes) {
        uploadFile(fileName, bytes);
        return getUrlByKey(fileName);
    }

    /**
     * 上传文件并获取其URL地址
     *
     * @param fileName 文件名，用于指定要上传的文件名称
     * @param content  文件内容的字符串
     * @return 返回上传文件的URL地址，通过这个URL可以访问到上传的文件
     */
    public String getUploadContentUrl(String fileName, String content) {
        uploadContent(fileName, content, false);
        return getUrlByKey(fileName);
    }

    /**
     * 上传文件并获取其URL地址
     *
     * @param fileName 文件名，用于指定要上传的文件名称
     * @param content  文件内容的字符串
     * @return 返回上传文件的URL地址，通过这个URL可以访问到上传的文件
     */
    public String getPrivateUploadContentUrl(String fileName, String content) {
        uploadContent(fileName, content, true);
        return getUrlByKey(fileName);
    }

    /**
     * 上传文件并获取预签名URL
     *
     * @param fileName       文件名，用于标识要上传的文件
     * @param bytes          文件的字节流，包含文件的具体内容
     * @param expirationDate 预签名URL的过期时间，超过这个时间URL将无法访问文件
     * @return 返回一个预签名URL，通过这个URL可以在有效期内访问上传的文件
     */
    public String getUploadPresignedUrl(String fileName, byte[] bytes, Date expirationDate) {
        uploadFile(fileName, bytes);
        return getPresignedUrlByKey(fileName, expirationDate);
    }

    /**
     * 上传文件
     *
     * @param fileName 文件名，用于在存储桶中标识文件
     * @param bytes    文件内容的字节数组
     * @return 返回上传后的文件对象信息
     * @throws IllegalArgumentException 如果文件上传过程中发生错误，则抛出此异常
     */
    public PutObjectResult uploadFile(String fileName, byte[] bytes) {
        try (InputStream is = new ByteArrayInputStream(bytes)) {
            //获取文件的类型
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentLength(bytes.length);
            PutObjectRequest putObjectRequest = new PutObjectRequest(cosProperties.getBucket(), fileName, is, objectMetadata);
            return cosClient.putObject(putObjectRequest);
        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw new COSException("文件上传失败");
        }
    }

    /**
     * 上传文件
     *
     * @param fileName 文件名，用于在存储桶中标识文件
     * @param content  文件内容的字符串
     * @return 返回上传后的文件对象信息
     * @throws IllegalArgumentException 如果文件上传过程中发生错误，则抛出此异常
     */
    public PutObjectResult uploadContent(String fileName, String content, boolean isPrivate) {
        byte[] contentByteArray = content.getBytes(StringUtils.UTF8);
        String contentMd5 = Md5Utils.md5AsBase64(contentByteArray);
        InputStream contentInput = new ByteArrayInputStream(contentByteArray);
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentLength(contentByteArray.length);
        objectMetadata.setContentMD5(contentMd5);
        objectMetadata.setContentDisposition("inline");
        objectMetadata.setCacheControl("no-cache");
        PutObjectRequest putObjectRequest = new PutObjectRequest(cosProperties.getBucket(), fileName, contentInput, objectMetadata);
        if (isPrivate) {
            putObjectRequest.withCannedAcl(CannedAccessControlList.Private);
        }
        return cosClient.putObject(putObjectRequest);
    }

    /**
     * 上传文件到腾讯云COS服务
     *
     * @param originalFilename 原始文件名，用于获取文件类型
     * @param fileSize         文件大小，用于设置ObjectMetadata
     * @param inputStream      文件输入流，用于上传文件
     * @return 返回上传文件的URL地址
     */
    private String uploadFile(String originalFilename, Long fileSize, InputStream inputStream) {
        try (InputStream is = inputStream) {
            //获取文件的类型
            String fileType = originalFilename.substring(originalFilename.lastIndexOf("."));
            //使用UUID工具  创建唯一名称，放置文件重名被覆盖，在拼接上上命令获取的文件类型
            //int pos = originalFilename.lastIndexOf(".");
            //originalFilename.substring(0, pos) + "_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + originalFilename.substring(pos);
            String fileName = UUID.randomUUID() + fileType;
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentLength(fileSize);
            PutObjectRequest putObjectRequest = new PutObjectRequest(cosProperties.getBucket(), fileName, is, objectMetadata);
            PutObjectResult putResult = cosClient.putObject(putObjectRequest);
            return cosClient.getObjectUrl(cosProperties.getBucket(), fileName).toString();
        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw new COSException("文件上传失败");
        }
    }

    /**
     * 获取COS（Cloud Object Storage）的临时凭证
     * 该方法用于生成一个具有临时权限的COS凭证，用于访问腾讯云对象存储服务
     * 它根据配置的密钥信息、桶信息、区域信息以及定义的权限策略来获取临时凭证
     *
     * @return COSCredential 对象，包含访问COS所需的临时凭证信息
     * @throws IllegalArgumentException 如果提供的密钥信息无效，抛出此异常
     */
    public COSCredential getCredential(String[] actions) {
        TreeMap<String, Object> config = new TreeMap<>();
        config.put("secretId", cosProperties.getSecretId());
        config.put("secretKey", cosProperties.getSecretKey());
        // 设置域名:如果您使用了腾讯云 cvm，可以设置内部域名
        //config.put("host", "sts.internal.tencentcloudapi.com");
        // 临时密钥有效时长，单位是秒，默认 1800 秒，目前主账号最长 2 小时（即 7200 秒），子账号最长 36 小时（即 129600）秒
        config.put("durationSeconds", 1800);
        config.put("bucket", cosProperties.getBucket());
        config.put("region", cosProperties.getRegion());

        // 开始构建一条 statement
        Statement statement = new Statement();
        // 声明设置的结果是允许操作
        statement.setEffect("allow");
        /**
         * 密钥的权限列表。必须在这里指定本次临时密钥所需要的权限。
         * 权限列表请参见 https://cloud.tencent.com/document/product/436/31923
         * 规则为 {project}:{interfaceName}
         * project : 产品缩写  cos相关授权为值为cos,数据万象(数据处理)相关授权值为ci
         * 授权所有接口用*表示，例如 cos:*,ci:*
         * 添加一批操作权限 :
         */
        statement.addActions(actions);
        /**
         * 这里改成允许的路径前缀，可以根据自己网站的用户登录态判断允许上传的具体路径
         * 资源表达式规则分对象存储(cos)和数据万象(ci)两种
         * 数据处理、审核相关接口需要授予ci资源权限
         *  cos : qcs::cos:{region}:uid/{appid}:{bucket}/{path}
         *  ci  : qcs::ci:{region}:uid/{appid}:bucket/{bucket}/{path}
         * 列举几种典型的{path}授权场景：
         * 1、允许访问所有对象："*"
         * 2、允许访问指定的对象："a/a1.txt", "b/b1.txt"
         * 3、允许访问指定前缀的对象："a*", "a/*", "b/*"
         *  如果填写了“*”，将允许用户访问所有资源；除非业务需要，否则请按照最小权限原则授予用户相应的访问权限范围。
         *
         * 示例：授权examplebucket-1250000000 bucket目录下的所有资源给cos和ci 授权两条Resource
         */
        statement.addResources(new String[]{"*"});

        // 把一条 statement 添加到 policy
        Policy policy = new Policy();
        policy.addStatement(statement);
        // 将 Policy 示例转化成 String，可以使用任何 json 转化方式，这里是本 SDK 自带的推荐方式
        config.put("policy", Jackson.toJsonPrettyString(policy));
        Response credential;
        try {
            credential = CosStsClient.getCredential(config);
        } catch (IOException e) {
            log.error("获取COS临时凭证异常", e);
            throw new COSCredentialException();
        }
        COSCredential cosCredential = new COSCredential();
        cosCredential.setExpiration(credential.expiration);
        cosCredential.setStartTime(credential.startTime);
        cosCredential.setExpiredTime(credential.expiredTime);
        cosCredential.setTmpSecretId(credential.credentials.tmpSecretId);
        cosCredential.setTmpSecretKey(credential.credentials.tmpSecretKey);
        cosCredential.setSessionToken(credential.credentials.sessionToken);
        return cosCredential;
    }

    /**
     * 根据URL获取对象键
     *
     * @param url 对象的URL地址
     * @return 对象的键，即对象在存储桶中的唯一标识符
     */
    public String getObjectKeyByUrl(String url) {
        if (StringUtils.isNullOrEmpty(url)) {
            throw new IllegalArgumentException("URL不能为空");
        }
        // 构建API端点URL
        String apiEndpoint = String.format("%s://%s/",
                cosClient.getClientConfig().getHttpProtocol(),
                cosClient.getClientConfig().getEndpointBuilder().buildGeneralApiEndpoint(cosProperties.getBucket()));
        // 判断是完整URL还是对象键
        String objectKey = url.contains(apiEndpoint) ? url.substring(apiEndpoint.length()) : url;
        log.debug("对象标识符:{} --- {}", url, objectKey);
        return objectKey;
    }

    /**
     * 根据URL获取文件内容
     *
     * @param url 对象的URL地址
     * @return 文件内容
     */
    public String getDownloadContentByUrl(String url) {
        String objectKey = getObjectKeyByUrl(url);
        GetObjectRequest getObjectRequest = new GetObjectRequest(cosProperties.getBucket(), objectKey);
        try {
            COSObject cosObject = cosClient.getObject(getObjectRequest);
            try (InputStream cosObjectInput = cosObject.getObjectContent()) {
                return IOUtils.toString(cosObjectInput);
            }
        } catch (IOException e) {
            log.error("读取COS文件内容失败，URL: {}", url, e);
            throw new COSException("读取文件失败");
        }
    }

    /**
     * 复制COS对象到新的路径
     *
     * @param sourceUrl 源文件URL
     * @param targetUrl 目标文件URL
     * @return 目标文件的完整URL
     */
    public String copyObject(String sourceUrl, String targetUrl) {
        if (StringUtils.isNullOrEmpty(sourceUrl) || StringUtils.isNullOrEmpty(targetUrl)) {
            throw new IllegalArgumentException("源URL和目标URL不能为空");
        }
        try {
            String sourceKey = getObjectKeyByUrl(sourceUrl);
            String targetKey = getObjectKeyByUrl(targetUrl);
            log.debug("开始复制COS对象，源键: {}, 目标键: {}", sourceKey, targetKey);
            // 构建复制源
            CopyObjectRequest copyObjectRequest = new CopyObjectRequest(
                    cosProperties.getBucket(), // 源存储桶
                    sourceKey,                  // 源对象键
                    cosProperties.getBucket(),  // 目标存储桶
                    targetKey                   // 目标对象键
            );
            // 执行复制操作
            CopyObjectResult copyResult = cosClient.copyObject(copyObjectRequest);
            log.info("COS对象复制成功，源: {}, 目标: {}, ETag: {}", sourceKey, targetKey, copyResult.getETag());
            // 返回目标文件的完整URL
            return getUrlByKey(targetKey);
        } catch (Exception e) {
            log.error("复制COS对象失败，源URL: {}, 目标文件URL: {}", sourceUrl, targetUrl, e);
            throw new COSException("文件复制失败: " + e.getMessage());
        }
    }
}
