package com.unipus.digitalbook.common.utils;

import java.util.*;

/**
 * 数组处理工具类
 */
public class ArrayUtil {
    private ArrayUtil(){
        super();
    }

    /**
     * 合并多个数组并去重，支持可变参数，兼容 null 和空数组
     *
     * @param arrays 待合并的数组集合
     * @return 合并后的唯一数组
     */
    @SuppressWarnings("unchecked")
    public static <T> T[] mergeAllArrays(T[]... arrays) {
        if (arrays == null || arrays.length == 0) {
            return (T[]) new Object[0];
        }

        Set<T> uniqueElements = new LinkedHashSet<>();
        for (T[] array : arrays) {
            if (array != null && array.length > 0) {
                Collections.addAll(uniqueElements, array);
            }
        }

        return uniqueElements.toArray(Arrays.copyOf(arrays[0], 0));
    }

    /**
     * 判断两个集合内容是否相同，忽略顺序，null 和 空集合视为相同
     */
    public static <T> boolean equalsIgnoreNullAndOrder(Collection<T> a, Collection<T> b) {
        return normalize(a).equals(normalize(b));
    }

    private static <T> Set<T> normalize(Collection<T> collection) {
        return (collection == null || collection.isEmpty())
                ? Collections.emptySet()
                : new HashSet<>(collection);
    }
}
