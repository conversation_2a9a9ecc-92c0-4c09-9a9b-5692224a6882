package com.unipus.digitalbook.common.utils;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

@Slf4j
public class AESUtil {
    private static final byte[] IV_DEFAULT = new byte[]{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16};
    private static final String ALGORITHM = "AES";
    // 工作模式：算法名称/加密模式/数据填充方式
    private static final String TRANSFORM_CBC_PKCS5 = "AES/CBC/PKCS5Padding";

    /**
     * 加密
     *
     * @param content  需要加密的内容
     * @param password 加密密码
     * @return 加密后的数据
     */
    public static String encryptByAES(String password, String content) {
        try {
            Cipher cipher = Cipher.getInstance(TRANSFORM_CBC_PKCS5);
            SecretKeySpec skeySpec = new SecretKeySpec(password.getBytes(), ALGORITHM);
            // 使用CBC模式，需要一个向量iv，可增加加密算法的强度
            IvParameterSpec ivp = new IvParameterSpec(IV_DEFAULT);
            cipher.init(Cipher.ENCRYPT_MODE, skeySpec, ivp);
            byte[] encrypted = cipher.doFinal(content.getBytes());
            return parseByte2HexStr(encrypted);
        } catch (Exception e) {
            log.error("AES加密失败!");
        }
        return null;
    }

    /**
     * 解密
     *
     * @param content  待解密内容
     * @param password 解密密钥
     * @return
     */
    public static String decryptByAES(String password, String content) {
        if (content == null || content.trim().isEmpty()) {
            return null;
        }
        try {
            SecretKeySpec skeySpec = new SecretKeySpec(password.getBytes(), ALGORITHM);
            Cipher cipher = Cipher.getInstance(TRANSFORM_CBC_PKCS5);
            IvParameterSpec ivp = new IvParameterSpec(IV_DEFAULT);
            cipher.init(Cipher.DECRYPT_MODE, skeySpec, ivp);
            byte[] result = cipher.doFinal(parseHexStr2Byte(content));
            return new String(result);
        } catch (Exception e) {
            log.error("AES解密失败!");
        }
        return null;
    }

    // 将二进制转换成16进制
    private static String parseByte2HexStr(byte[] buf) {
        StringBuilder sb = new StringBuilder();
        for (byte b : buf) {
            String hex = Integer.toHexString(b & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            sb.append(hex.toUpperCase());
        }
        return sb.toString();
    }

    // 将16进制转换为二进制
    private static byte[] parseHexStr2Byte(String hexStr) {
        if (hexStr==null || hexStr.isEmpty()) {
            throw new RuntimeException("hex string is empty.");
        }
        byte[] result = new byte[hexStr.length() / 2];
        for (int i = 0; i < hexStr.length() / 2; i++) {
            int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
            int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2), 16);
            result[i] = (byte) (high * 16 + low);
        }
        return result;
    }


    public static void main(String[] args) {
        String decryptResult = decryptByAES("EiBBCfPzEMHvLQnN", "579BB1F3F7A32B3E9B16E1D12169460D7D890C0F5056E574B4DF75B63DF3C309F15C5ABB8B1625B59758DFC13483C3D849D4B800923DF5E5E07167263E9829F9");
        log.info("解密后：{}", decryptResult);
    }
}
