package com.unipus.digitalbook.common.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.unipus.digitalbook.common.exception.org.RedisCatchException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class RedisUtil {

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 从缓存中读取列表数据
     * @param redisKey 缓存key
     * @param clazz 列表数据类型
     * @return 列表数据
     * @param <T> 列表数据类型
     */
    public <T> List<T> readListFromBucket(String redisKey, Class<T> clazz) {
        try {
            RBucket<String> bucket = redissonClient.getBucket(redisKey);
            if (bucket==null || !bucket.isExists() || bucket.get()==null) {
                return null;
            }
            String jsonString = bucket.get();
            CollectionType collectionType = objectMapper.getTypeFactory().constructCollectionType(ArrayList.class, clazz);
            return objectMapper.readValue(jsonString, collectionType);
        } catch (Exception e) {
            log.error("解析缓存失败，key: {}", redisKey, e);
            throw new RedisCatchException("解析组织树缓存失败", e);
        }
    }

    /**
     * 将列表数据写入缓存
     * @param redisKey 缓存key
     * @param dataList 列表数据
     * @param timeout 超时时间（单位：秒）
     * @param <T> 列表数据类型
     */
    public <T> void writeListToBucket(String redisKey, List<T> dataList, Long timeout) {
        try {
            if (CollectionUtils.isEmpty(dataList)) {
                throw new RedisCatchException("组织树为空");
            }
            RBucket<String> bucket = redissonClient.getBucket(redisKey);
            if (bucket==null) {
                throw new RedisCatchException("redisson bucket is null");
            }
            String jsonString = objectMapper.writeValueAsString(dataList);
            if (timeout == null) {
                bucket.set(jsonString);
            } else {
                bucket.set(jsonString, Duration.ofSeconds(timeout));
            }
        } catch (Exception e) {
            log.error("序列化组织树失败，key: {}", redisKey, e);
            throw new RedisCatchException("序列化组织树失败", e);
        }
    }

    /**
     * 从缓存中删除数据
     * @param redisKey 缓存key
     */
    public void deleteFromBucket(String redisKey) {
        RBucket<String> bucket = redissonClient.getBucket(redisKey);
        if (bucket.isExists()) {
            bucket.delete();
        }
    }

    /**
     * 从缓存中读取任意对象
     * @param redisKey 缓存key
     * @param clazz 列表数据类型
     * @return 列表数据
     * @param <R> 数据类型
     */
    public <R> R readFromBucket(String redisKey, Class<R> clazz) {
        RBucket<R> bucket = redissonClient.getBucket(redisKey);
        if (bucket==null || !bucket.isExists()) {
            return null;
        }
        return bucket.get();
    }

    /**
     * 将任意对象写入缓存
     * @param redisKey 缓存key
     * @param data 数据
     * @param timeout 超时时间（单位：秒）
     * @param <R> 数据类型
     */
    public <R> void writeToBucket(String redisKey, R data, Long timeout) {
        RBucket<R> bucket = redissonClient.getBucket(redisKey);
        if(timeout == null) {
            bucket.set(data);
        }else {
            bucket.set(data, Duration.ofSeconds(timeout));
        }
    }

}
