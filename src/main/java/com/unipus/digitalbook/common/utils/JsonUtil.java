package com.unipus.digitalbook.common.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.TypeReference;
import com.alibaba.fastjson2.filter.ContextAutoTypeBeforeHandler;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class JsonUtil {

    private static final ContextAutoTypeBeforeHandler handler = new ContextAutoTypeBeforeHandler(true) {
        @Override
        public Class<?> apply(String typeName, Class<?> expectClass, long features) {
            try {
                return Class.forName(typeName);
            } catch (ClassNotFoundException e) {
                log.error("typeName {} class not found", typeName, e);
            }
            return super.apply(typeName, expectClass, features);
        }
    };

    public static String toJsonString(Object object) {
        if (object == null) {
            return null;
        }
        return JSON.toJSONString(object);
    }

    public static String toPrettyString(Object object) {
        if (object == null) {
            return null;
        }
        return JSON.toJSONString(object, com.alibaba.fastjson2.JSONWriter.Feature.PrettyFormat);
    }

    public static <T> T parseObject(String json, Class<T> clazz) {
        return JSON.parseObject(json, clazz);
    }

    public static <T> T parseObject(String json, TypeReference<T> typeReference) {
        return JSON.parseObject(json, typeReference);
    }

    public static String writeValueAsString(Object obj) {
        return JSON.toJSONString(obj, JSONWriter.Feature.WriteClassName);
    }

    public static <T> T readValue(String json, TypeReference<T> typeReference) {
        return JSON.parseObject(json, typeReference, handler);
    }

}
