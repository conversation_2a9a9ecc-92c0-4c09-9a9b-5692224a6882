package com.unipus.digitalbook.common.exception.book;

public class ChapterCreationException extends RuntimeException {

    /**
     * Default constructor with a predefined message.
     */
    public ChapterCreationException() {
        super("添加章节版本失败");
    }

    /**
     * Constructor with a custom message.
     *
     * @param message the custom error message
     */
    public ChapterCreationException(String message) {
        super(message);
    }

    /**
     * Constructor with a custom message and a cause.
     *
     * @param message the custom error message
     * @param cause the throwable that caused this exception
     */
    public ChapterCreationException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * Constructor with a cause.
     *
     * @param cause the throwable that caused this exception
     */
    public ChapterCreationException(Throwable cause) {
        super("添加章节版本失败", cause);
    }
}