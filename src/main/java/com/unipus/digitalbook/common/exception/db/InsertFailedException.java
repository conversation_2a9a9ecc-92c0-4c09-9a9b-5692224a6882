package com.unipus.digitalbook.common.exception.db;

public class InsertFailedException extends RuntimeException {

    public InsertFailedException() {
        super("Insert operation failed: The number of affected rows is less than or equal to zero.");
    }

    public InsertFailedException(String message) {
        super(message);
    }

    public InsertFailedException(String message, Throwable cause) {
        super(message, cause);
    }

    public InsertFailedException(Throwable cause) {
        super(cause);
    }
}
