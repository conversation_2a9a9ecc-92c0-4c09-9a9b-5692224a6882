package com.unipus.digitalbook.common.exception.user;

/**
 * 用户状态异常，用于表示用户状态不符合业务逻辑的情况。
 */
public class UserStateException extends RuntimeException {

    /**
     * 构造一个新的用户状态异常。
     *
     * @param message 异常信息
     */
    public UserStateException(String message) {
        super(message);
    }

    /**
     * 构造一个新的用户状态异常。
     *
     * @param message 异常信息
     * @param cause   异常原因
     */
    public UserStateException(String message, Throwable cause) {
        super(message, cause);
    }
}
