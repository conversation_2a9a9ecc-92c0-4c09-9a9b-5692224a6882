package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.dao.BookKnowledgeInfoMapper;
import com.unipus.digitalbook.dao.BookKnowledgeSourceInfoMapper;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.service.KnowledgeLabelService;
import com.unipus.digitalbook.service.remote.restful.knowledge.KnowledgeApiService;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.PaginationResponse;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.ResourceTag;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.BaseKnowledgeResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 图谱创建相关接口
 */
@Service
@Slf4j
public class KnowledgeLabelServiceImpl implements KnowledgeLabelService {

    @Resource
    KnowledgeApiService knowledgeApiService;

    @Resource
    BookKnowledgeInfoMapper bookKnowledgeInfoMapper;

    @Resource
    BookKnowledgeSourceInfoMapper bookKnowledgeSourceInfoMapper;

    @Override
    public Response<PaginationResponse> labelList(String keyword, Integer pageNum, Integer pageSize) {
        BaseKnowledgeResponse<PaginationResponse> remoteRes = knowledgeApiService.labelList(keyword, pageNum, pageSize);
        return Response.success(remoteRes.getResult());
    }

    @Override
    public Response<ResourceTag> labelAdd(ResourceTag params) {
        BaseKnowledgeResponse<ResourceTag> remoteRes = knowledgeApiService.labelAdd(params);
        return Response.success(remoteRes.getResult());
    }

    @Override
    public Response<ResourceTag> labelUpdate(ResourceTag params) {
        BaseKnowledgeResponse<ResourceTag> remoteRes = knowledgeApiService.labelUpdate(params);
        return Response.success(remoteRes.getResult());
    }

    @Override
    public Response labelDelete(String labelId) {
        knowledgeApiService.labelDelete(labelId);
        return Response.success();
    }
}
