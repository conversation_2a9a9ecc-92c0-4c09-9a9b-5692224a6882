package com.unipus.digitalbook.service.impl;

import com.alibaba.fastjson2.JSON;
import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.dao.CustomContentPOMapper;
import com.unipus.digitalbook.dao.CustomContentQuestionGroupRelationPOMapper;
import com.unipus.digitalbook.model.constants.CacheConstant;
import com.unipus.digitalbook.model.entity.content.CopyContentItem;
import com.unipus.digitalbook.model.entity.content.CustomContent;
import com.unipus.digitalbook.model.entity.content.CustomContentNode;
import com.unipus.digitalbook.model.entity.content.PublishContentItem;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.enums.CustomContentEventEnum;
import com.unipus.digitalbook.model.enums.CustomContentStatusEnum;
import com.unipus.digitalbook.model.events.CustomContentEvent;
import com.unipus.digitalbook.model.po.content.CustomContentNodePO;
import com.unipus.digitalbook.model.po.content.CustomContentPO;
import com.unipus.digitalbook.model.po.content.CustomContentQuestionGroupRelationPO;
import com.unipus.digitalbook.service.COSService;
import com.unipus.digitalbook.service.CustomContentService;
import com.unipus.digitalbook.service.QuestionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CustomContentServiceImpl implements CustomContentService {

    private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    @Resource
    private CustomContentPOMapper customContentPOMapper;
    @Resource
    private CustomContentQuestionGroupRelationPOMapper customContentQuestionGroupRelationPOMapper;
    @Resource
    private QuestionService questionService;
    @Resource
    private COSService cosService;
    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;
    @Value("${kafka.topic.customContent}")
    private String topicCustomContent;

    /**
     * 保存自建内容
     *
     * @param customContent   自建内容实体
     * @param contentPackage  内容数据包
     * @param dataPackage     数据包
     * @return 保存结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveCustomContent(CustomContent customContent, String contentPackage, String dataPackage) {
        // 参数验证
        if (customContent == null) {
            throw new IllegalArgumentException("自建内容参数不能为空");
        }
        if (customContent.getTenantId() == null) {
            throw new IllegalArgumentException("租户ID不能为空");
        }
        String bizId = customContent.getBizId();
        Long tenantId = customContent.getTenantId();
        log.info("开始保存自建内容，bizId={}，tenantId={}，name={}", bizId, tenantId, customContent.getName());
        CustomContentPO customContentPO = null;
        if (StringUtils.isBlank(bizId)) {
            bizId = IdentifierUtil.getShortUUID();
        } else {
            customContentPO = customContentPOMapper.selectEditingByBizId(bizId, tenantId);
        }
        // 新增或更新
        if (customContentPO == null) {
            return handleInsertCustomContent(customContent, bizId, contentPackage, dataPackage);
        } else {
            return handleUpdateCustomContent(customContent, customContentPO, contentPackage, dataPackage);
        }
    }

    /**
     * 处理新增自建内容
     */
    private Boolean handleInsertCustomContent(CustomContent customContent, String bizId, String contentPackage, String dataPackage) {
        log.info("新增自建内容，bizId={}，name={}", bizId, customContent.getName());
        CustomContentPO insertCustomContentPO = new CustomContentPO(customContent);
        insertCustomContentPO.setBizId(bizId);
        insertCustomContentPO.setStatus(CustomContentStatusEnum.EDITING.getCode());
        insertCustomContentPO.setCreateTime(new Date());
        insertCustomContentPO.setUpdateBy(null);
        // 上传自建内容到COS
        addCustomContentUploadUrl(insertCustomContentPO);
        // 插入记录
        int row = customContentPOMapper.insertSelective(insertCustomContentPO);
        if (row > 0) {
            // 保存题目关联关系
            addCustomContentQuestions(customContent.getQuestionList(), insertCustomContentPO.getId(),
                    customContent.getTenantId(), insertCustomContentPO.getCreateBy());
            // 发布自建内容新增消息
            sendCustomContentEvent(insertCustomContentPO, contentPackage, dataPackage, CustomContentEventEnum.ADD);
            log.info("新增自建内容成功，bizId={}", bizId);
            return true;
        } else {
            log.warn("新增自建内容失败，数据库插入返回0行，bizId={}", bizId);
            return false;
        }
    }

    /**
     * 处理更新自建内容
     */
    private Boolean handleUpdateCustomContent(CustomContent customContent, CustomContentPO existingPO, String contentPackage, String dataPackage) {
        String bizId = customContent.getBizId();
        log.info("更新自建内容，bizId={}，name={}", bizId, customContent.getName());
        CustomContentPO updateCustomContentPO = new CustomContentPO(customContent);
        updateCustomContentPO.setId(existingPO.getId());
        updateCustomContentPO.setUpdateTime(new Date());
        // 上传自建内容到COS
        addCustomContentUploadUrl(updateCustomContentPO);
        // 更新记录
        int row = customContentPOMapper.updateByPrimaryKeySelective(updateCustomContentPO);
        if (row > 0) {
            // 更新题目关联关系：先删除旧的关联关系，再添加新的关联关系
            addCustomContentQuestions(customContent.getQuestionList(), updateCustomContentPO.getId(),
                    customContent.getTenantId(), updateCustomContentPO.getUpdateBy());
            // 发布自建内容编辑消息
            sendCustomContentEvent(updateCustomContentPO, contentPackage, dataPackage, CustomContentEventEnum.ADD);
            log.info("更新自建内容成功，bizId={}", bizId);
            return true;
        } else {
            log.warn("更新自建内容失败，数据库更新返回0行，bizId={}", bizId);
            return false;
        }
    }

    /**
     * 批量保存自建内容
     *
     * @param contentPackageMap 自建内容与内容数据包的映射
     * @param dataPackage       数据包
     * @return 保存结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveBatchCustomContent(Map<CustomContent, String> contentPackageMap, String dataPackage) {
        if (contentPackageMap == null || contentPackageMap.isEmpty()) {
            return false;
        }
        for (Map.Entry<CustomContent, String> entry : contentPackageMap.entrySet()) {
            CustomContent customContent = entry.getKey();
            String contentPackage = entry.getValue();
            saveCustomContent(customContent, contentPackage, dataPackage);
        }
        return true;
    }

    /**
     * 添加自建内容题目关联关系
     * 区分null和空集合：null时不进行更新操作，非null时进行关联关系的删除和重建
     *
     * @param questionList 题目列表
     * @param contentId    自建内容ID
     * @param tenantId     租户ID
     * @param userId       用户ID
     */
    private void addCustomContentQuestions(List<BigQuestionGroup> questionList, Long contentId, Long tenantId, Long userId) {
        if (questionList == null) {
            return;
        }
        log.info("处理题目关联关系，contentId={}，题目数量：{}", contentId, questionList.size());
        // 当questionList不为null时（包括空集合），先删除旧的关联关系（逻辑删除）
        customContentQuestionGroupRelationPOMapper.deleteByContentId(contentId, tenantId, userId);

        if (CollectionUtils.isEmpty(questionList)) {
            return;
        }
        log.info("题目列表:{}", JsonUtil.toJsonString(questionList));
        List<Long> newGroupIds = questionService.batchSaveBigQuestions(questionList);
        if (newGroupIds.isEmpty()) {
            log.warn("保存题目失败，未获得题组ID，contentId={}", contentId);
            return;
        }
        log.info("题组列表：{}", JsonUtil.toJsonString(newGroupIds));
        // 保存自建内容ID和题的关系
        List<CustomContentQuestionGroupRelationPO> relationPOList = newGroupIds.stream().distinct().map(
                        groupId -> new CustomContentQuestionGroupRelationPO(groupId, contentId, tenantId, userId))
                .toList();
        log.info("保存自建内容ID和题的关系:{}", JsonUtil.toJsonString(relationPOList));
        customContentQuestionGroupRelationPOMapper.batchInsert(relationPOList);
    }

    /**
     * 发布自建内容消息
     *
     * @param customContentPO 自建内容实体
     * @param contentPackage  内容数据包
     * @param dataPackage     数据包
     */
    private void sendCustomContentEvent(CustomContentPO customContentPO, String contentPackage, String dataPackage, CustomContentEventEnum event) {
        CustomContentEvent customContentEvent = new CustomContentEvent(customContentPO, contentPackage, dataPackage, event);
        kafkaTemplate.send(topicCustomContent, JsonUtil.toJsonString(customContentEvent));
    }

    /**
     * 上传自建内容到COS
     *
     * @param customContentPO 自建内容PO
     */
    private void addCustomContentUploadUrl(CustomContentPO customContentPO) {
        if (customContentPO == null) {
            return;
        }
        if (StringUtils.isBlank(customContentPO.getContent())
                && StringUtils.isBlank(customContentPO.getStudentContent())) {
            return;
        }
        try (ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            String timestamp = getTimestamp();

            String content = customContentPO.getContent();
            if (StringUtils.isNotBlank(content) && JSON.isValid(content)) {
                // 计算内容的MD5哈希值
                String newContentHashCode = DigestUtils.md5Hex(content);
                String oldContentHashCode = customContentPO.getContentHashCode();
                // 只有当内容发生变化时才上传到COS
                if (!Objects.equals(newContentHashCode, oldContentHashCode)) {
                    futures.add(CompletableFuture.runAsync(() -> {
                        String fileName = String.format("/customContent/editing/%s/%s/content.json",
                                customContentPO.getBizId(), timestamp);
                        String contentUrl = cosService.getPrivateUploadContentUrl(fileName, content);
                        customContentPO.setContent(contentUrl);
                        log.info("内容发生变化，上传自建内容到COS,bizId={},url={}", customContentPO.getBizId(), customContentPO.getContent());
                    }, executor));
                    // 更新哈希码
                    customContentPO.setContentHashCode(newContentHashCode);
                }
            }

            String studentContent = customContentPO.getStudentContent();
            if (StringUtils.isNotBlank(studentContent) && JSON.isValid(studentContent)) {
                // 计算学生内容的MD5哈希值
                String newStudentContentHashCode = DigestUtils.md5Hex(studentContent);
                String oldStudentContentHashCode = customContentPO.getStudentContentHashCode();
                // 只有当学生内容发生变化时才上传到COS
                if (!Objects.equals(newStudentContentHashCode, oldStudentContentHashCode)) {
                    futures.add(CompletableFuture.runAsync(() -> {
                        String fileName = String.format("/customContent/editing/%s/%s/studentContent.json",
                                customContentPO.getBizId(), timestamp);
                        String studentContentUrl = cosService.getPrivateUploadContentUrl(fileName, studentContent);
                        customContentPO.setStudentContent(studentContentUrl);
                        log.info("学生内容发生变化，上传自建学生内容到COS,bizId={},url={}", customContentPO.getBizId(), customContentPO.getStudentContent());
                    }, executor));
                    // 更新哈希码
                    customContentPO.setStudentContentHashCode(newStudentContentHashCode);
                }
            }

            // 等待所有上传任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }
    }

    /**
     * 为自建内容设置预签名URL
     *
     * @param customContentPOList 自建内容PO列表
     */
    private void getCustomContentPresignedUrl(List<CustomContentPO> customContentPOList) {
        if (CollectionUtils.isEmpty(customContentPOList)) {
            return;
        }
        customContentPOList.forEach(this::getCustomContentPresignedUrl);
    }

    /**
     * 为自建内容设置预签名URL
     *
     * @param customContentPO 自建内容PO
     */
    private void getCustomContentPresignedUrl(CustomContentPO customContentPO) {
        if (customContentPO == null) {
            return;
        }
        if (StringUtils.isBlank(customContentPO.getContent())
                && StringUtils.isBlank(customContentPO.getStudentContent())) {
            return;
        }
        // 使用虚拟线程
        try (ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            String content = customContentPO.getContent();
            if (StringUtils.isNotBlank(content) && !JSON.isValid(content)) {
                futures.add(CompletableFuture.runAsync(() -> customContentPO.setContent(
                        cosService.getCachePresignedUrlByUrl(content)), executor));
            }

            String studentContent = customContentPO.getStudentContent();
            if (StringUtils.isNotBlank(studentContent) && !JSON.isValid(studentContent)) {
                futures.add(CompletableFuture.runAsync(() -> customContentPO.setStudentContent(
                        cosService.getCachePresignedUrlByUrl(studentContent)), executor));
            }
            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }
    }


    /**
     * 根据URL获取自建内容
     *
     * @param contentUrl 内容URL地址
     * @return 自建内容字符串，如果URL为空则返回null
     */
    private String getCustomContentByUrl(String contentUrl) {
        return cosService.getCacheDownloadContentByUrl(contentUrl,
                CacheConstant.REDIS_COS_CHAPTER_CONTENT_PREFIX,
                CacheConstant.REDIS_CHAPTER_CONTENT_TIMEOUT_SECONDS);
    }

    /**
     * 更新自建内容名称
     *
     * @param customContent   自建内容实体
     * @param contentPackage  内容数据包
     * @param dataPackage     数据包
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateCustomContentName(CustomContent customContent, String contentPackage, String dataPackage) {
        // 参数验证
        if (customContent == null) {
            throw new IllegalArgumentException("自建内容参数不能为空");
        }
        if (customContent.getTenantId() == null) {
            throw new IllegalArgumentException("租户ID不能为空");
        }
        log.info("更新自建内容名称，bizId={}，新名称={}", customContent.getBizId(), customContent.getName());
        // 根据bizId查询现有记录
        CustomContentPO existingPO = customContentPOMapper.selectEditingByBizId(customContent.getBizId(), customContent.getTenantId());
        if (existingPO == null) {
            throw new IllegalArgumentException("自建内容不存在");
        }
        // 更新名称和更新时间
        existingPO.setName(customContent.getName());
        existingPO.setUpdateTime(new Date());
        existingPO.setUpdateBy(customContent.getUpdateBy());
        boolean result = customContentPOMapper.updateByPrimaryKeySelective(existingPO) > 0;
        if (result) {
            // 发布自建内容编辑消息
            sendCustomContentEvent(existingPO, contentPackage, dataPackage, CustomContentEventEnum.ADD);
        }
        return result;
    }

    /**
     * 根据业务ID获取编写中的自建内容
     *
     * @param bizId    业务ID
     * @param tenantId 租户ID
     * @return 自建内容实体
     */
    @Override
    public CustomContent getEditingCustomContentByBizId(String bizId, Long tenantId) {
        CustomContentPO po = customContentPOMapper.selectEditingByBizId(bizId, tenantId);
        if (po == null) {
            throw new IllegalArgumentException("自建内容不存在");
        }
        // 为内容设置预签名URL
        getCustomContentPresignedUrl(po);
        return po.toEntity();
    }

    /**
     * 根据业务ID列表批量获取编写中的自建内容
     *
     * @param bizIds   业务ID列表
     * @param tenantId 租户ID
     * @return 自建内容列表
     */
    @Override
    public List<CustomContent> getEditingCustomContentByBizIds(List<String> bizIds, Long tenantId) {
        if (CollectionUtils.isEmpty(bizIds)) {
            return List.of();
        }
        List<CustomContentPO> customContentPOList = customContentPOMapper.selectEditingByBizIds(bizIds, tenantId);
        if (CollectionUtils.isEmpty(customContentPOList)) {
            return List.of();
        }
        // 为内容设置预签名URL
        getCustomContentPresignedUrl(customContentPOList);
        return customContentPOList.stream()
                .map(CustomContentPO::toEntity)
                .toList();
    }

    /**
     * 根据业务ID获取编写中的自建内容简化信息（不包含content、studentContent）
     *
     * @param bizId    业务ID
     * @param tenantId 租户ID
     * @return 自建内容实体
     */
    @Override
    public CustomContent getEditingCustomContentSimpleByBizId(String bizId, Long tenantId) {
        log.info("获取编写中的自建内容简化信息，bizId={}，tenantId={}", bizId, tenantId);
        CustomContentPO po = customContentPOMapper.selectEditingByBizId(bizId, tenantId);
        if (po == null) {
            throw new IllegalArgumentException("自建内容不存在");
        }
        // 清空content和studentContent字段，不调用COS获取预签名URL
        po.setContent(null);
        po.setStudentContent(null);
        return po.toEntity();
    }

    /**
     * 根据业务ID列表批量获取编写中的自建内容简化信息（不包含content、studentContent）
     *
     * @param bizIds   业务ID列表
     * @param tenantId 租户ID
     * @return 自建内容列表
     */
    @Override
    public List<CustomContent> getEditingCustomContentSimpleByBizIds(List<String> bizIds, Long tenantId) {
        log.info("批量获取编写中的自建内容简化信息，bizIds={}，tenantId={}", bizIds, tenantId);
        if (CollectionUtils.isEmpty(bizIds)) {
            return List.of();
        }
        List<CustomContentPO> customContentPOList = customContentPOMapper.selectEditingByBizIds(bizIds, tenantId);
        if (CollectionUtils.isEmpty(customContentPOList)) {
            return List.of();
        }
        // 清空content和studentContent字段，不调用COS获取预签名URL
        customContentPOList.forEach(po -> {
            po.setContent(null);
            po.setStudentContent(null);
        });
        return customContentPOList.stream()
                .map(CustomContentPO::toEntity)
                .toList();
    }

    /**
     * 根据业务ID获取已发布的自建内容
     *
     * @param bizId    业务ID
     * @param tenantId 租户ID
     * @return 自建内容实体
     */
    @Override
    public CustomContent getPublishedCustomContentByBizId(String bizId, Long tenantId) {
        CustomContentPO po = customContentPOMapper.selectPublishedByBizId(bizId, tenantId);
        if (po == null) {
            throw new IllegalArgumentException("自建内容不存在");
        }
        // 为内容设置预签名URL
        getCustomContentPresignedUrl(po);
        return po.toEntity();
    }

    /**
     * 根据业务ID列表批量获取已发布的自建内容
     *
     * @param bizIds   业务ID列表
     * @param tenantId 租户ID
     * @return 自建内容列表
     */
    @Override
    public List<CustomContent> getPublishedCustomContentByBizIds(List<String> bizIds, Long tenantId) {
        if (CollectionUtils.isEmpty(bizIds)) {
            return List.of();
        }
        List<CustomContentPO> customContentPOList = customContentPOMapper.selectPublishedByBizIds(bizIds, tenantId);
        if (CollectionUtils.isEmpty(customContentPOList)) {
            return List.of();
        }
        // 为内容设置预签名URL
        getCustomContentPresignedUrl(customContentPOList);
        return customContentPOList.stream()
                .map(CustomContentPO::toEntity)
                .toList();
    }

    /**
     * 根据业务ID获取已发布的自建内容简化信息（不包含content、studentContent）
     *
     * @param bizId    业务ID
     * @param tenantId 租户ID
     * @return 自建内容实体
     */
    @Override
    public CustomContent getPublishedCustomContentSimpleByBizId(String bizId, Long tenantId) {
        log.info("获取已发布的自建内容简化信息，bizId={}，tenantId={}", bizId, tenantId);
        CustomContentPO po = customContentPOMapper.selectPublishedByBizId(bizId, tenantId);
        if (po == null) {
            throw new IllegalArgumentException("自建内容不存在");
        }
        // 清空content和studentContent字段，不调用COS获取预签名URL
        po.setContent(null);
        po.setStudentContent(null);
        return po.toEntity();
    }

    /**
     * 根据业务ID列表批量获取已发布的自建内容简化信息（不包含content、studentContent）
     *
     * @param bizIds   业务ID列表
     * @param tenantId 租户ID
     * @return 自建内容列表
     */
    @Override
    public List<CustomContent> getPublishedCustomContentSimpleByBizIds(List<String> bizIds, Long tenantId) {
        log.info("批量获取已发布的自建内容简化信息，bizIds={}，tenantId={}", bizIds, tenantId);
        if (CollectionUtils.isEmpty(bizIds)) {
            return List.of();
        }
        List<CustomContentPO> customContentPOList = customContentPOMapper.selectPublishedByBizIds(bizIds, tenantId);
        if (CollectionUtils.isEmpty(customContentPOList)) {
            return List.of();
        }
        // 清空content和studentContent字段，不调用COS获取预签名URL
        customContentPOList.forEach(po -> {
            po.setContent(null);
            po.setStudentContent(null);
        });
        return customContentPOList.stream()
                .map(CustomContentPO::toEntity)
                .toList();
    }

    /**
     * 根据业务ID列表批量删除自建内容
     *
     * @param bizIds      业务ID列表
     * @param tenantId    租户ID
     * @param userId      用户ID
     * @param dataPackage 数据包
     * @return 删除结果
     */
    @Override
    public Boolean deleteCustomContentByBizIds(List<String> bizIds, Long tenantId, Long userId, String dataPackage) {
        if (CollectionUtils.isEmpty(bizIds)) {
            return false;
        }
        // 先查询要删除的内容信息，用于发布事件
        List<CustomContentPO> customContentPOList = customContentPOMapper.selectEditingByBizIds(bizIds, tenantId);
        boolean result = customContentPOMapper.deleteEditingByBizIds(bizIds, tenantId, userId) > 0;
        if (result) {
            // 发布自建内容发布删除消息
            for (CustomContentPO deletedPO : customContentPOList) {
                sendCustomContentEvent(deletedPO, dataPackage, dataPackage, CustomContentEventEnum.DELETE);
            }
        }
        return result;
    }

    /**
     * 发布自建内容
     *
     * @param publishContentList 要发布的自建内容列表
     * @param deleteContentList  要删除的自建内容列表
     * @param tenantId           租户ID
     * @param userId             用户ID
     * @param dataPackage        数据包
     * @return 发布结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean publishCustomContent(List<PublishContentItem> publishContentList, List<PublishContentItem> deleteContentList, Long tenantId, Long userId, String dataPackage) {
        List<String> publishContentIds = publishContentList != null ? publishContentList.stream().map(PublishContentItem::getBizId).toList() : null;
        List<String> deleteContentIds = deleteContentList != null ? deleteContentList.stream().map(PublishContentItem::getBizId).toList() : null;
        long startTime = System.currentTimeMillis();
        log.info("开始发布自建内容，发布数量：{}，删除数量：{}",
                CollectionUtils.isEmpty(publishContentIds) ? 0 : publishContentIds.size(),
                CollectionUtils.isEmpty(deleteContentIds) ? 0 : deleteContentIds.size());
        // 发布内容：复制编写中的数据，新增为已发布状态，如果存在已发布状态的数据先删除后新增
        if (!CollectionUtils.isEmpty(publishContentIds)) {
            List<CustomContentPO> customContentPOList = customContentPOMapper.selectEditingByBizIds(publishContentIds, tenantId);
            if (!CollectionUtils.isEmpty(customContentPOList)) {
                // 使用虚拟线程池并行处理COS文件复制
                long cosElapsedTime = parallelCopyCosFiles(customContentPOList, "customContent/published/%s/%s/");
                log.info("发布自建内容，COS文件复制完成，耗时：{}ms", cosElapsedTime);
                // 批量插入已发布记录
                // 建立bizId到原始contentId的映射关系（发布时bizId保持不变，用于复制题目关联关系）
                Map<String, Long> bizIdToOriginalIdMap = customContentPOList.stream()
                        .collect(HashMap::new, (map, po) -> map.put(po.getBizId(), po.getId()), HashMap::putAll);
                // 先查询出已发布数据，然后删除自建内容和题组关系
                List<String> bizIds = customContentPOList.stream().map(CustomContentPO::getBizId).toList();
                deletePublishedContentAndRelations(bizIds, tenantId, userId);
                // 批量设置发布状态
                Date currentTime = new Date();
                customContentPOList.forEach(po -> {
                    po.setStatus(CustomContentStatusEnum.PUBLISHED.getCode());
                    po.setCreateTime(currentTime);
                    po.setCreateBy(userId);
                    po.setUpdateBy(null);
                    po.setUpdateTime(null);
                    po.setId(null);
                });
                int insertCount = customContentPOMapper.batchInsert(customContentPOList);
                log.info("批量发布自建内容成功，数量: {}", insertCount);
                // 复制题目关联关系（发布时bizId不变，直接通过bizId获取原始contentId）
                copyQuestionGroupRelations(bizIdToOriginalIdMap, customContentPOList, tenantId, userId);
                if (insertCount > 0) {
                    // 构建bizId到contentPackage的映射
                    Map<String, String> publishContentPackageMap = buildBizIdToContentPackageMap(publishContentList);
                    // 发布自建内容发布新增消息
                    for (CustomContentPO publishedPO : customContentPOList) {
                        // 获取对应的contentPackage
                        String contentPackage = publishContentPackageMap.get(publishedPO.getBizId());
                        sendCustomContentEvent(publishedPO, contentPackage, dataPackage, CustomContentEventEnum.ADD);
                    }
                }
            }
        }
        // 删除已发布的内容
        if (!CollectionUtils.isEmpty(deleteContentIds)) {
            // 先查询要删除的内容信息，用于发布事件
            List<CustomContentPO> toDeleteContentList = customContentPOMapper.selectPublishedByBizIds(deleteContentIds, tenantId);
            int deleteCount = customContentPOMapper.deletePublishedByBizIds(deleteContentIds, tenantId, userId);
            log.info("删除已发布内容，数量: {}", deleteCount);
            // 发布自建内容发布删除消息
            if (deleteCount > 0) {
                // 构建bizId到contentPackage的映射
                Map<String, String> deleteContentPackageMap = buildBizIdToContentPackageMap(deleteContentList);
                for (CustomContentPO deletedPO : toDeleteContentList) {
                    // 获取对应的contentPackage
                    String contentPackage = deleteContentPackageMap.get(deletedPO.getBizId());
                    sendCustomContentEvent(deletedPO, contentPackage, dataPackage, CustomContentEventEnum.DELETE);
                }
            }
        }
        log.info("发布自建内容完成，总耗时：{}ms", System.currentTimeMillis() - startTime);
        return true;
    }

    /**
     * 复制自建内容
     *
     * @param copyContentList 自建内容复制项列表
     * @param status          内容状态（0：编写中/1：待发布/2：已发布）
     * @param tenantId        租户ID
     * @param userId          当前用户ID
     * @param dataPackage     数据包
     * @return 复制前后bizId的映射关系 Map<原bizId, 新bizId>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, String> copyCustomContentByBizIds(List<CopyContentItem> copyContentList, Integer status, Long tenantId, Long userId, String dataPackage) {
        List<String> bizIds = copyContentList != null ? copyContentList.stream().map(CopyContentItem::getBizId).toList() : null;
        if (CollectionUtils.isEmpty(bizIds)) {
            return new HashMap<>();
        }
        if (status == null) {
            throw new IllegalArgumentException("内容状态不能为空");
        }
        long startTime = System.currentTimeMillis();
        log.info("开始复制自建内容，数量: {}，状态: {}", bizIds.size(), status);
        // 根据status参数查询指定状态的内容
        List<CustomContentPO> contentList = customContentPOMapper.selectByBizIdsAndStatus(bizIds, status, tenantId);
        if (CollectionUtils.isEmpty(contentList)) {
            log.warn("未找到指定的内容，bizIds: {}", bizIds);
            return new HashMap<>();
        }
        // 建立完整的ID映射关系（原始bizId -> 原始contentId, 新bizId -> 原始contentId）
        Map<String, String> bizIdMapping = new HashMap<>(contentList.size());
        Map<String, Long> newBizIdToOriginalIdMap = new HashMap<>(contentList.size());
        for (CustomContentPO contentPO : contentList) {
            String originalBizId = contentPO.getBizId();
            Long originalContentId = contentPO.getId();
            String newBizId = IdentifierUtil.getShortUUID();
            // 建立bizId映射关系（用于返回结果）
            bizIdMapping.put(originalBizId, newBizId);
            // 建立新bizId到原始contentId的直接映射（用于复制题目关联关系）
            newBizIdToOriginalIdMap.put(newBizId, originalContentId);

            contentPO.setBizId(newBizId);
        }
        // 使用虚拟线程池并行处理COS文件复制
        long cosElapsedTime = parallelCopyCosFiles(contentList, "customContent/editing/%s/%s/");
        log.info("复制自建内容，COS文件复制完成，耗时：{}ms", cosElapsedTime);
        // 批量插入编写中记录
        if (!contentList.isEmpty()) {
            contentList.forEach(po -> {
                // 设置为编写中状态
                po.setStatus(CustomContentStatusEnum.EDITING.getCode());
                po.setCreateTime(new Date());
                po.setUpdateTime(null);
                po.setCreateBy(userId);
                po.setUpdateBy(null);
                po.setId(null);
            });
            int insertCount = customContentPOMapper.batchInsert(contentList);
            log.info("批量复制内容成功，数量: {}", insertCount);
            // 复制题目关联关系
            copyQuestionGroupRelations(newBizIdToOriginalIdMap, contentList, tenantId, userId);
            if (insertCount > 0) {
                // 构建原始bizId到contentPackage的映射
                Map<String, String> copyContentPackageMap = buildBizIdToContentPackageMapForCopy(copyContentList);
                // 发布自建内容新增消息
                for (CustomContentPO copiedPO : contentList) {
                    // 通过bizIdMapping获取原始bizId，然后获取对应的contentPackage
                    String originalBizId = bizIdMapping.entrySet().stream()
                            .filter(entry -> entry.getValue().equals(copiedPO.getBizId()))
                            .map(Map.Entry::getKey)
                            .findFirst()
                            .orElse(copiedPO.getBizId());
                    String contentPackage = copyContentPackageMap.get(originalBizId);
                    sendCustomContentEvent(copiedPO, contentPackage, dataPackage, CustomContentEventEnum.ADD);
                }
            }
        }
        log.info("复制自建内容完成，总耗时：{}ms，成功数量: {}", System.currentTimeMillis() - startTime, bizIdMapping.size());
        return bizIdMapping;
    }

    @Override
    public Map<String, CustomContentNode> getNodeMapByBizId(String bizId) {
        if (StringUtils.isBlank(bizId)) {
            return Collections.emptyMap();
        }
        CustomContentPO customContentPO = customContentPOMapper.selectContentNodeByBizId(bizId);
        if (customContentPO == null) {
            return Collections.emptyMap();
        }
        List<CustomContentNodePO> totalStruct = customContentPO.getTotalStruct();
        if (CollectionUtils.isEmpty(totalStruct)) {
            return Collections.emptyMap();
        }
        Map<String, CustomContentNode> nodeIndexMap = new HashMap<>();
        for (int i = 0; i < totalStruct.size(); i++) {
            CustomContentNode entity = totalStruct.get(i).toEntity();
            entity.setOffset(i);
            nodeIndexMap.put(totalStruct.get(i).getId(), entity);
        }
        return nodeIndexMap;
    }

    @Override
    public Map<String, CustomContentNode> getQuestionNodeMapByBizId(String bizId) {
        Map<String, CustomContentNode> nodeIndexMap = new HashMap<>();
        getNodeMapByBizId(bizId).values().forEach(node -> {
            if (StringUtils.isNotBlank(node.getQuestionType())) {
                nodeIndexMap.put(node.getText(), node);
            }
        });
        return nodeIndexMap;
    }

    /**
     * 复制content文件
     *
     * @param customContentPO 自建内容PO
     * @param targetPath      目标路径模板（如：customContent/published/%s/%s/）
     * @param timestamp       预生成的时间戳
     */
    private void copyContentFile(CustomContentPO customContentPO, String targetPath, String timestamp) {
        String bizId = customContentPO.getBizId();
        // 复制content文件
        if (StringUtils.isNotBlank(customContentPO.getContent()) && !JSON.isValid(customContentPO.getContent())) {
            String contentPath = String.format(targetPath + "content.json", bizId, timestamp);
            String contentUrl = cosService.copyObject(customContentPO.getContent(), contentPath);
            customContentPO.setContent(contentUrl);
        }
    }

    /**
     * 复制studentContent文件
     *
     * @param customContentPO 自建内容PO
     * @param targetPath      目标路径模板（如：customContent/published/%s/%s/）
     * @param timestamp       预生成的时间戳
     */
    private void copyStudentContentFile(CustomContentPO customContentPO, String targetPath, String timestamp) {
        String bizId = customContentPO.getBizId();
        // 复制studentContent文件
        if (StringUtils.isNotBlank(customContentPO.getStudentContent()) && !JSON.isValid(customContentPO.getStudentContent())) {
            String studentContentPath = String.format(targetPath + "studentContent.json", bizId, timestamp);
            String studentContentUrl = cosService.copyObject(customContentPO.getStudentContent(), studentContentPath);
            customContentPO.setStudentContent(studentContentUrl);
        }
    }

    private String getTimestamp() {
        return LocalDateTime.now().format(TIMESTAMP_FORMATTER);
    }

    /**
     * 并行复制COS文件
     *
     * @param customContentPOList 自建内容列表
     * @param pathTemplate        路径模板（如：customContent/published/%s/%s/ 或 customContent/editing/%s/%s/）
     * @return 耗时（毫秒）
     */
    private long parallelCopyCosFiles(List<CustomContentPO> customContentPOList, String pathTemplate) {
        long cosStartTime = System.currentTimeMillis();
        try (ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {
            String timestamp = getTimestamp();
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            for (CustomContentPO customContentPO : customContentPOList) {
                // 分别复制content和studentContent文件
                futures.add(CompletableFuture.runAsync(() -> copyContentFile(customContentPO, pathTemplate, timestamp), executor));
                futures.add(CompletableFuture.runAsync(() -> copyStudentContentFile(customContentPO, pathTemplate, timestamp), executor));
            }
            // 等待所有COS文件复制任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }
        return System.currentTimeMillis() - cosStartTime;
    }


    /**
     * 根据内容ID获取题目列表
     *
     * @param contentId 内容ID
     * @param tenantId  租户ID
     * @return 题目列表
     */
    @Override
    public List<BigQuestionGroup> getQuestionListByContentId(Long contentId, Long tenantId) {
        Map<Long, List<BigQuestionGroup>> questionMap = getQuestionMapByContentIds(List.of(contentId), tenantId);
        return questionMap.get(contentId);
    }

    /**
     * 根据内容ID列表获取题目映射关系
     *
     * @param contentIds 内容ID列表
     * @param tenantId   租户ID
     * @return 内容ID到题目列表的映射关系 Map<内容ID, 题目列表>
     */
    @Override
    public Map<Long, List<BigQuestionGroup>> getQuestionMapByContentIds(List<Long> contentIds, Long tenantId) {
        if (CollectionUtils.isEmpty(contentIds)) {
            return new HashMap<>();
        }
        // 获取内容与题组的关联关系
        List<CustomContentQuestionGroupRelationPO> relationPOList = customContentQuestionGroupRelationPOMapper.selectByContentIds(contentIds, tenantId);
        if (CollectionUtils.isEmpty(relationPOList)) {
            return new HashMap<>();
        }
        // 提取所有相关的题组ID并去重
        List<Long> questionGroupIds = relationPOList.stream()
                .map(CustomContentQuestionGroupRelationPO::getGroupId)
                .distinct()
                .toList();
        // 批量获取所有题组信息
        List<BigQuestionGroup> allQuestions = questionService.batchGetBigQuestions(questionGroupIds);
        // 构建题组ID到题组对象的映射
        Map<Long, BigQuestionGroup> questionMap = allQuestions.stream()
                .collect(Collectors.toMap(BigQuestionGroup::getId, q -> q));
        // 按contentId分组，并提取对应的题组列表
        return relationPOList.stream()
                .collect(Collectors.groupingBy(
                        CustomContentQuestionGroupRelationPO::getContentId,
                        Collectors.mapping(
                                relation -> questionMap.get(relation.getGroupId()),
                                Collectors.toList()
                        )
                ));
    }

    /**
     * 构建bizId到contentPackage的映射关系（PublishContentItem版本）
     *
     * @param contentList  内容列表
     * @return bizId到contentPackage的映射
     */
    private Map<String, String> buildBizIdToContentPackageMap(List<PublishContentItem> contentList) {
        if (contentList == null) {
            return new HashMap<>();
        }
        return contentList.stream()
                .collect(HashMap::new, (map, item)
                        -> map.put(item.getBizId(), item.getContentPackage()), HashMap::putAll);
    }

    /**
     * 构建bizId到contentPackage的映射关系（CopyContentItem版本）
     *
     * @param contentList 内容列表
     * @return bizId到contentPackage的映射
     */
    private Map<String, String> buildBizIdToContentPackageMapForCopy(List<CopyContentItem> contentList) {
        if (contentList == null) {
            return new HashMap<>();
        }
        return contentList.stream()
                .collect(HashMap::new, (map, item)
                        -> map.put(item.getBizId(), item.getContentPackage()), HashMap::putAll);
    }

    /**
     * 删除已发布内容及其关联关系
     *
     * @param bizIds   业务ID列表
     * @param tenantId 租户ID
     * @param userId   用户ID
     */
    private void deletePublishedContentAndRelations(List<String> bizIds, Long tenantId, Long userId) {
        List<CustomContentPO> existingPublishedList = customContentPOMapper.selectPublishedByBizIds(bizIds, tenantId);
        if (!existingPublishedList.isEmpty()) {
            // 删除已发布内容的题组关系
            List<Long> publishedContentIds = existingPublishedList.stream().map(CustomContentPO::getId).toList();
            for (Long contentId : publishedContentIds) {
                customContentQuestionGroupRelationPOMapper.deleteByContentId(contentId, tenantId, userId);
            }
            // 删除已发布的自建内容
            customContentPOMapper.deletePublishedByBizIds(bizIds, tenantId, userId);
        }
    }

    /**
     * 复制题目关联关系
     *
     * @param originalIdMap       原始ID映射关系 (bizId -> 原始contentId)
     * @param customContentPOList 新的自建内容列表
     * @param tenantId            租户ID
     * @param userId              用户ID
     */
    private void copyQuestionGroupRelations(Map<String, Long> originalIdMap, List<CustomContentPO> customContentPOList, Long tenantId, Long userId) {
        if (originalIdMap.isEmpty() || customContentPOList.isEmpty()) {
            return;
        }

        // 批量查询所有原始内容的题目关联关系
        List<Long> originalContentIds = new ArrayList<>(originalIdMap.values());
        List<CustomContentQuestionGroupRelationPO> allOriginalRelations =
                customContentQuestionGroupRelationPOMapper.selectByContentIds(originalContentIds, tenantId);

        if (allOriginalRelations.isEmpty()) {
            return;
        }

        // 按contentId分组，提高查找效率
        Map<Long, List<CustomContentQuestionGroupRelationPO>> relationsByContentId = allOriginalRelations
                .stream()
                .collect(Collectors.groupingBy(CustomContentQuestionGroupRelationPO::getContentId));

        List<CustomContentQuestionGroupRelationPO> newRelations = new ArrayList<>();
        for (CustomContentPO newPO : customContentPOList) {
            // 获取原始contentId
            Long originalContentId = originalIdMap.get(newPO.getBizId());
            if (originalContentId == null || newPO.getId() == null) {
                continue;
            }

            // 查询原始内容的题目关联关系
            List<CustomContentQuestionGroupRelationPO> originalRelations = relationsByContentId.get(originalContentId);
            if (originalRelations != null) {
                for (CustomContentQuestionGroupRelationPO originalRelation : originalRelations) {
                    CustomContentQuestionGroupRelationPO newRelation =
                            new CustomContentQuestionGroupRelationPO(originalRelation.getGroupId(), newPO.getId(), tenantId, userId);
                    newRelations.add(newRelation);
                }
            }
        }

        if (!newRelations.isEmpty()) {
            customContentQuestionGroupRelationPOMapper.batchInsert(newRelations);
            log.info("复制题目关联关系成功，数量: {}", newRelations.size());
        }
    }

    /**
     * 根据业务ID列表批量获取编写中的自建内容目录信息
     *
     * @param bizIds   业务ID列表
     * @param tenantId 租户ID
     * @return 自建内容目录信息列表
     */
    @Override
    public List<CustomContent> getEditingCustomContentCatalogByBizIds(List<String> bizIds, Long tenantId) {
        if (CollectionUtils.isEmpty(bizIds)) {
            return List.of();
        }
        List<CustomContentPO> customContentPOList = customContentPOMapper.selectEditingCatalogByBizIds(bizIds, tenantId);
        if (CollectionUtils.isEmpty(customContentPOList)) {
            return List.of();
        }
        return customContentPOList.stream()
                .map(CustomContentPO::toEntity)
                .toList();
    }

    /**
     * 根据业务ID列表批量获取已发布的自建内容目录信息
     *
     * @param bizIds   业务ID列表
     * @param tenantId 租户ID
     * @return 自建内容目录信息列表
     */
    @Override
    public List<CustomContent> getPublishedCustomContentCatalogByBizIds(List<String> bizIds, Long tenantId) {
        if (CollectionUtils.isEmpty(bizIds)) {
            return List.of();
        }
        List<CustomContentPO> customContentPOList = customContentPOMapper.selectPublishedCatalogByBizIds(bizIds, tenantId);
        if (CollectionUtils.isEmpty(customContentPOList)) {
            return List.of();
        }
        return customContentPOList.stream()
                .map(CustomContentPO::toEntity)
                .toList();
    }
}