package com.unipus.digitalbook.service.remote.restful.sso.response;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;

import java.util.Optional;

@XmlAccessorType(XmlAccessType.FIELD)
public class AuthenticationSuccess {

    @XmlElement(name = "user", namespace = "http://www.yale.edu/tp/cas")
    private String user;

    @XmlElement(name = "attributes", namespace = "http://www.yale.edu/tp/cas")
    private Attributes attributes;

    public String getUser() {
        return user;
    }

    public Optional<Attributes> getAttributes() {
        return Optional.ofNullable(attributes);
    }
}
