package com.unipus.digitalbook.service.remote.restful.engine;

import com.unipus.digitalbook.service.remote.restful.engine.model.BaseResponse;
import com.unipus.digitalbook.service.remote.restful.engine.model.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class EngineApiManger {
    @Resource
    private EngineApiService engineApiService;

    @Value("${remote.translate.appKey}")
    private String appKey;

    @Value("${remote.translate.appSecret}")
    private String appSecret;

    public BaseResponse<CorrectResponse> translate(String srcTxt, String tgtTxt, List<String> refs) {
        String langTgtTxt = !refs.isEmpty() ? refs.getFirst() : tgtTxt;
        TranslateLangParam translateLangParam = new TranslateLangParam(appKey, srcTxt, langTgtTxt);
        BaseResponse<TranslateLangResult> translateLang = engineApiService.getTranslateLang(translateLangParam);
        log.info("translateLangParam: {}, translateLang: {}", translateLangParam, translateLang);
        if (!translateLang.isSuccess()) {
            log.error("translateLang error {}, {}", translateLang, translateLangParam);
            throw new IllegalStateException("translateLang error");
        }
        if (translateLang.getData().getIs_supported() == null || !translateLang.getData().getIs_supported()) {
            throw new UnsupportedOperationException("translateLang not support");
        }
        String srcLang = translateLang.getData().getSrc_lang();
        String tgtLang = translateLang.getData().getTgt_lang();
        return engineApiService.translateSubmit(new TranslateParam(appKey, appSecret, srcLang, srcTxt, tgtLang, tgtTxt, refs));
    }
}
