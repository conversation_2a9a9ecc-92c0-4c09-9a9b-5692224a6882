package com.unipus.digitalbook.service.remote.restful.engine.model;

public class TranslateLangParam {
    private String src_txt;

    private String tgt_txt;

    private String task;

    private String appkey;

    public TranslateLangParam(){}

    public TranslateLangParam(String appKey, String srcTxt, String tgtTxt) {
        this.src_txt = srcTxt;
        this.tgt_txt = tgtTxt;
        this.task = "transl_score";
        this.appkey = appKey;
    }

    public String getSrc_txt() {
        return src_txt;
    }

    public void setSrc_txt(String src_txt) {
        this.src_txt = src_txt;
    }

    public String getTgt_txt() {
        return tgt_txt;
    }

    public void setTgt_txt(String tgt_txt) {
        this.tgt_txt = tgt_txt;
    }

    public String getTask() {
        return task;
    }

    public void setTask(String task) {
        this.task = task;
    }

    public String getAppkey() {
        return appkey;
    }

    public void setAppkey(String appkey) {
        this.appkey = appkey;
    }

    @Override
    public String toString() {
        return "TranslateLangParam{" +
                "src_txt='" + src_txt + '\'' +
                ", tgt_txt='" + tgt_txt + '\'' +
                ", task='" + task + '\'' +
                ", appkey='" + appkey + '\'' +
                '}';
    }
}
