package com.unipus.digitalbook.service.remote.restful.sso.response;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

import java.util.Optional;

@XmlRootElement(name = "serviceResponse", namespace = "http://www.yale.edu/tp/cas")
@XmlAccessorType(XmlAccessType.FIELD)
public class ServiceResponse {

    @XmlElement(name = "authenticationSuccess", namespace = "http://www.yale.edu/tp/cas")
    private AuthenticationSuccess authenticationSuccess;

    @XmlElement(name = "authenticationFailure", namespace = "http://www.yale.edu/tp/cas")
    private AuthenticationFailure authenticationFailure;

    public boolean isSuccess() {
        return authenticationSuccess != null;
    }

    public Optional<AuthenticationSuccess> getAuthenticationSuccess() {
        return Optional.ofNullable(authenticationSuccess);
    }

    public Optional<AuthenticationFailure> getAuthenticationFailure() {
        return Optional.ofNullable(authenticationFailure);
    }
}


