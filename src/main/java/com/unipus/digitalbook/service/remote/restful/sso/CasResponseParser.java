package com.unipus.digitalbook.service.remote.restful.sso;

import com.unipus.digitalbook.service.remote.restful.sso.response.Result;
import com.unipus.digitalbook.service.remote.restful.sso.response.ServiceResponse;
import com.unipus.digitalbook.service.remote.restful.sso.response.SsoResponse;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Unmarshaller;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.StringReader;

@Service
@Slf4j
public class CasResponseParser {
    private static final String ERROR_MESSAGE_TEMPLATE = """  
                        认证失败，
                        错误码:{}，
                        错误信息:{},
                        原始xml:
                        {}
                        """;



    /**
     * 解析XML格式的响应字符串，将其转换为ServiceResponse对象。
     *
     * @param xmlResponse XML格式的响应字符串
     * @return ServiceResponse对象，包含解析后的数据
     */
    public ServiceResponse parseResponse(String xmlResponse) {
        try {
            // 创建JAXB上下文，用于处理ServiceResponse类的XML解析
            JAXBContext context = JAXBContext.newInstance(ServiceResponse.class);

            // 创建Unmarshaller对象，用于将XML解析为Java对象
            Unmarshaller unmarshaller = context.createUnmarshaller();

            // 将响应字符串转换为StringReader，供Unmarshaller读取
            StringReader reader = new StringReader(xmlResponse);

            // 执行XML解析，将解析结果转换为ServiceResponse对象
            return (ServiceResponse) unmarshaller.unmarshal(reader);
        } catch (JAXBException e) {
            // 如果解析过程中发生JAXBException异常，则抛出运行时异常
            // 并将原始异常作为原因，便于后续的错误处理
            throw new RuntimeException("Failed to parse CAS response", e);
        }
    }

    /**
     * 处理响应的XML字符串，并返回SSO响应对象。
     *
     * @param xmlResponse XML格式的响应字符串
     * @return SsoResponse对象，包含处理后的响应信息
     */
    public SsoResponse processResponse(String xmlResponse) {
        // 解析XML响应字符串，获取ServiceResponse对象
        ServiceResponse response = parseResponse(xmlResponse);

        // 创建SsoResponse对象，用于存储处理后的响应信息
        SsoResponse ssoResponse = new SsoResponse();

        // 判断响应是否成功
        if (response.isSuccess()) {
            // 如果响应成功，获取认证成功信息
            response.getAuthenticationSuccess().ifPresent(success -> {
                // 创建Result对象，用于存储用户信息和属性
                Result result = new Result();

                // 获取用户信息，并设置到Result对象中
                String user = success.getUser();
                result.setUser(user);

                // 安全地获取和处理属性信息
                // 将属性信息设置到Result对象中
                success.getAttributes().ifPresent(result::setAttributes);

                // 将Result对象设置到SsoResponse对象中
                ssoResponse.setRs(result);

                // 设置响应代码为"0"，表示成功
                ssoResponse.setCode("0");
            });
        } else {
            // 如果响应失败，获取认证失败信息
            response.getAuthenticationFailure().ifPresent(failure -> {
                // 获取错误代码和错误信息
                String errorCode = failure.getCode();
                String errorMessage = failure.getMessage();
                log.debug(ERROR_MESSAGE_TEMPLATE,
                        errorCode,errorMessage.trim(),xmlResponse.trim());
                // 处理错误情况，设置响应代码和错误信息到SsoResponse对象中
                ssoResponse.setCode(errorCode);
                ssoResponse.setMsg(errorMessage.trim());
            });
        }

        // 返回处理后的SsoResponse对象
        return ssoResponse;
    }


}
