package com.unipus.digitalbook.service.remote.restful.sso.response;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/23 18:15
 */
public class SsoUserResponse {
    private String code;
    private String msg;
    private Rs rs;

    public Boolean getStatus() {
        return "0".equals(code);
    }

    public static class Rs {
        private String grantingTicket;
        private String serviceTicket;
        private long tgtExpiredTime;
        private String role;
        private String openid;
        private String nickname;
        private String fullname;
        private String username;
        private String mobile;
        private String email;
        private String perms;
        private String isSsoLogin;
        private String isCompleted;
        private String openidHash;
        private String jwt;
        private String rt;
        private long createTime;
        private int status;
        private String source;
        private List<Link> links;

        // Getters and Setters

        public String getGrantingTicket() {
            return grantingTicket;
        }

        public void setGrantingTicket(String grantingTicket) {
            this.grantingTicket = grantingTicket;
        }

        public String getServiceTicket() {
            return serviceTicket;
        }

        public void setServiceTicket(String serviceTicket) {
            this.serviceTicket = serviceTicket;
        }

        public long getTgtExpiredTime() {
            return tgtExpiredTime;
        }

        public void setTgtExpiredTime(long tgtExpiredTime) {
            this.tgtExpiredTime = tgtExpiredTime;
        }

        public String getRole() {
            return role;
        }

        public void setRole(String role) {
            this.role = role;
        }

        public String getOpenid() {
            return openid;
        }

        public void setOpenid(String openid) {
            this.openid = openid;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public String getFullname() {
            return fullname;
        }

        public void setFullname(String fullname) {
            this.fullname = fullname;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getMobile() {
            return mobile;
        }

        public void setMobile(String mobile) {
            this.mobile = mobile;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getPerms() {
            return perms;
        }

        public void setPerms(String perms) {
            this.perms = perms;
        }

        public String getIsSsoLogin() {
            return isSsoLogin;
        }

        public void setIsSsoLogin(String isSsoLogin) {
            this.isSsoLogin = isSsoLogin;
        }

        public String getIsCompleted() {
            return isCompleted;
        }

        public void setIsCompleted(String isCompleted) {
            this.isCompleted = isCompleted;
        }

        public String getOpenidHash() {
            return openidHash;
        }

        public void setOpenidHash(String openidHash) {
            this.openidHash = openidHash;
        }

        public String getJwt() {
            return jwt;
        }

        public void setJwt(String jwt) {
            this.jwt = jwt;
        }

        public String getRt() {
            return rt;
        }

        public void setRt(String rt) {
            this.rt = rt;
        }

        public long getCreateTime() {
            return createTime;
        }

        public void setCreateTime(long createTime) {
            this.createTime = createTime;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getSource() {
            return source;
        }

        public void setSource(String source) {
            this.source = source;
        }

        public List<Link> getLinks() {
            return links;
        }

        public void setLinks(List<Link> links) {
            this.links = links;
        }
    }

    public static class Link {
        private String rel;
        private String href;

        // Getters and Setters

        public String getRel() {
            return rel;
        }

        public void setRel(String rel) {
            this.rel = rel;
        }

        public String getHref() {
            return href;
        }

        public void setHref(String href) {
            this.href = href;
        }
    }

    // Getters and Setters

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Rs getRs() {
        return rs;
    }

    public void setRs(Rs rs) {
        this.rs = rs;
    }
}