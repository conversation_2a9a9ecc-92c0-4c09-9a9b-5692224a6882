package com.unipus.digitalbook.service.remote.restful.engine.model;

public class TranslateCorrectResult {

    private String uuid;
    private String code;
    private double score;

    private double time_cost;

    private int waiting_jobs_num;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public double getScore() {
        return score;
    }

    public void setScore(double score) {
        this.score = score;
    }

    public double getTime_cost() {
        return time_cost;
    }

    public void setTime_cost(double time_cost) {
        this.time_cost = time_cost;
    }

    public int getWaiting_jobs_num() {
        return waiting_jobs_num;
    }

    public void setWaiting_jobs_num(int waiting_jobs_num) {
        this.waiting_jobs_num = waiting_jobs_num;
    }

    @Override
    public String toString() {
        return "TranslateCorrectResult{" +
                "uuid='" + uuid + '\'' +
                ", code='" + code + '\'' +
                ", score=" + score +
                ", time_cost=" + time_cost +
                ", waiting_jobs_num=" + waiting_jobs_num +
                '}';
    }
}
