package com.unipus.digitalbook.service.remote.restful.engine.model;

public class TranslateLangResult {

    private String src_lang;
    private String tgt_lang;
    private Boolean is_supported;

    public String getSrc_lang() {
        return src_lang;
    }

    public void setSrc_lang(String src_lang) {
        this.src_lang = src_lang;
    }

    public String getTgt_lang() {
        return tgt_lang;
    }

    public void setTgt_lang(String tgt_lang) {
        this.tgt_lang = tgt_lang;
    }

    public Boolean getIs_supported() {
        return is_supported;
    }

    public void setIs_supported(Boolean is_supported) {
        this.is_supported = is_supported;
    }


    @Override
    public String toString() {
        return "TranslateLangResult{" +
                "src_lang='" + src_lang + '\'' +
                ", tgt_lang='" + tgt_lang + '\'' +
                ", is_supported=" + is_supported +
                '}';
    }
}
