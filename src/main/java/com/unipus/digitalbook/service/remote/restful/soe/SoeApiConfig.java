package com.unipus.digitalbook.service.remote.restful.soe;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;
import reactor.core.publisher.Mono;

import java.time.Duration;

/**
 * SOE接口服务配置类
 *
 * <AUTHOR>
 * @date 2025/2/13 15:33
 */
@Slf4j
@Configuration
public class SoeApiConfig {

    @Value("${remote.soe.url}")
    private String soeApiUrl;

    /**
     * 创建并配置一个soeApi服务实例。
     *
     * @return 配置好的soeApi服务实例
     */
    @Bean
    public SoeApiService soeApiService() {
        // 创建一个 ExchangeFilterFunction 来记录请求信息
        ExchangeFilterFunction logFilter = ExchangeFilterFunction.ofRequestProcessor(clientRequest -> {
            // 获取完整的URL（包含查询参数）
            String fullUrl = clientRequest.url().toString();
            // 打印请求方法和URL
            log.info("Request: {} {}", clientRequest.method(), fullUrl);
            // 如果需要打印请求头
            clientRequest.headers().forEach((name, values)
                    -> values.forEach(value -> log.info("Request Header: {}={}", name, value)));
            return Mono.just(clientRequest);
        });

        // 使用WebClient.Builder构建一个WebClient实例，并设置最大内存大小为16MB
        WebClient webClient = WebClient.builder()
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(16 * 1024 * 1024))
                .baseUrl(soeApiUrl) // 设置基础URL
                .filter(logFilter)
//                .filter(logResponseFilter)
                .build();

        // 将WebClient转换为WebClientAdapter
        WebClientAdapter adapter = WebClientAdapter.create(webClient);

        // 设置请求超时时间为10秒
        adapter.setBlockTimeout(Duration.ofSeconds(10));

        // 使用HttpServiceProxyFactory创建一个HTTP服务代理工厂
        HttpServiceProxyFactory factory = HttpServiceProxyFactory.builderFor(adapter)
                .build();

        // 使用工厂创建一个SOEApiService客户端实例
        return factory.createClient(SoeApiService.class);
    }
}
