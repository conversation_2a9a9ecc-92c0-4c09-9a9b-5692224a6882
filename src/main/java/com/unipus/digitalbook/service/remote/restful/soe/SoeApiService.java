package com.unipus.digitalbook.service.remote.restful.soe;

import com.alibaba.fastjson2.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import java.util.Map;

/**
 * SOE接口
 *
 * <AUTHOR>
 * @date 2025/3/25 11:32
 */
@HttpExchange("/api")
public interface SoeApiService {

    // 初始化
    @PostExchange("/initialize/v2")
    ResponseEntity<JSONObject> initialize(@RequestHeader("auth") String auth, @RequestBody Map<String, Object> params);

    // 获取令牌
    @PostExchange("/acquire/v3")
    ResponseEntity<JSONObject> acquire(@RequestHeader("auth") String auth, @RequestBody Map<String, Object> params);

    // 释放令牌
    @PostExchange("/release/v2")
    ResponseEntity<JSONObject> release(@RequestHeader("auth") String auth, @RequestBody Map<String, Object> params);


    // 日志上报
    @PostExchange("/log/v2")
    ResponseEntity<JSONObject> log(@RequestHeader("auth") String auth, @RequestBody Map<String, Object> params);
}
