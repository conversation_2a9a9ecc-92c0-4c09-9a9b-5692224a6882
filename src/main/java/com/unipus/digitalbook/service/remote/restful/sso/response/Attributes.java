package com.unipus.digitalbook.service.remote.restful.sso.response;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;

import java.io.Serializable;
import java.util.StringJoiner;

@XmlAccessorType(XmlAccessType.FIELD)
public class Attributes implements Serializable {

    @XmlElement(name = "role", namespace = "http://www.yale.edu/tp/cas")
    private String role;

    @XmlElement(name = "mobile", namespace = "http://www.yale.edu/tp/cas")
    private String mobile;

    @XmlElement(name = "userid", namespace = "http://www.yale.edu/tp/cas")
    private String userid;

    @XmlElement(name = "phone", namespace = "http://www.yale.edu/tp/cas")
    private String phone;

    @XmlElement(name = "school", namespace = "http://www.yale.edu/tp/cas")
    private String school;

    @XmlElement(name = "nickname", namespace = "http://www.yale.edu/tp/cas")
    private String nickname;

    @XmlElement(name = "perms", namespace = "http://www.yale.edu/tp/cas")
    private String perms;

    @XmlElement(name = "fullname", namespace = "http://www.yale.edu/tp/cas")
    private String fullname;

    @XmlElement(name = "tempPassWordLogin", namespace = "http://www.yale.edu/tp/cas")
    private String tempPassWordLogin;

    @XmlElement(name = "username", namespace = "http://www.yale.edu/tp/cas")
    private String username;

    // Getters
    public String getRole() {
        return role;
    }

    public String getMobile() {
        return mobile;
    }

    public String getUserid() {
        return userid;
    }

    public String getPhone() {
        return phone;
    }

    public String getSchool() {
        return school;
    }

    public String getNickname() {
        return nickname;
    }

    public String getPerms() {
        return perms;
    }

    public String getFullname() {
        return fullname;
    }

    public String getTempPassWordLogin() {
        return tempPassWordLogin;
    }

    public String getUsername() {
        return username;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", Attributes.class.getSimpleName() + "[", "]")
                .add("role='" + role + "'")
                .add("mobile='" + mobile + "'")
                .add("userid='" + userid + "'")
                .add("phone='" + phone + "'")
                .add("school='" + school + "'")
                .add("nickname='" + nickname + "'")
                .add("perms='" + perms + "'")
                .add("fullname='" + fullname + "'")
                .add("tempPassWordLogin='" + tempPassWordLogin + "'")
                .add("username='" + username + "'")
                .toString();
    }
}
