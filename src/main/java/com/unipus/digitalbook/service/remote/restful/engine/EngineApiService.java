package com.unipus.digitalbook.service.remote.restful.engine;

import com.unipus.digitalbook.service.remote.restful.engine.model.BaseResponse;
import com.unipus.digitalbook.service.remote.restful.engine.model.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

/**
 * 引擎相关接口
 */
@HttpExchange("/api")
public interface EngineApiService {
    /**
     * 写作评测
     * @param params 评测
     * @return
     */
    @PostExchange("/engine/icorrect")
    BaseResponse<CorrectResponse> iCorrect(@RequestBody CorrectParam params);

    /**
     * 写作获取评测结果
     * @param id 评测id iCorrect接口返回的id
     * @return
     */
    @GetExchange("/engine/icorrect/{id}")
    BaseResponse<CorrectResult> get(@PathVariable String id);


    /**
     * 翻译评测
     * @return
     */
    @PostExchange("/translate/submit")
    BaseResponse<CorrectResponse> translateSubmit(@RequestBody TranslateParam params);

    /**
     * 翻译获取评测结果
     * @param id 评测id translateSubmit接口返回的id
     * @return
     */
    @GetExchange("/translate/{id}")
    BaseResponse<TranslateCorrectResult> getTranslateResult(@PathVariable String id);



    @PostExchange("/translate/lang")
    BaseResponse<TranslateLangResult> getTranslateLang(@RequestBody TranslateLangParam params);
}
