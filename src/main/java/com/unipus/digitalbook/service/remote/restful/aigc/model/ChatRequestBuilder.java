package com.unipus.digitalbook.service.remote.restful.aigc.model;

import java.util.ArrayList;
import java.util.List;

public class ChatRequestBuilder {
    public static ChatCompletionRequest buildImageRequest(String prompt, String imageUrl) {
        return buildRequest(null, prompt, imageUrl, "gpt-4o-mini");
    }

    public static ChatCompletionRequest buildRequest(String systemRole, String prompt, String imageUrl) {
        return buildRequest(systemRole, prompt, imageUrl, "gpt-4o");
    }

    public static ChatCompletionRequest buildRequest(String systemRole, String prompt, String imageUrl, String model) {
        if (prompt == null || prompt.isEmpty()) {
            throw new IllegalArgumentException("Prompt cannot be null or empty");
        }

        List<ChatMessage> chatMessages = new ArrayList<>();

        if (systemRole != null && !systemRole.isEmpty()) {
            ChatMessage message = new ChatMessage("system", List.of(new MessageContent("text", systemRole, null)));
            chatMessages.add(message);
        }

        List<MessageContent> contents = new ArrayList<>();
        contents.add(new MessageContent("text", prompt, null));

        if (imageUrl != null && !imageUrl.isEmpty()) {
            ImageUrl imgUrl = new ImageUrl(imageUrl);
            contents.add(new MessageContent("image_url", null, imgUrl));
        }

        chatMessages.add(new ChatMessage("user", contents));
        return new ChatCompletionRequest(model, chatMessages, 1000);
    }
}
