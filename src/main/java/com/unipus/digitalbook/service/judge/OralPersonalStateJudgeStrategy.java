package com.unipus.digitalbook.service.judge;

import com.alibaba.fastjson2.JSON;
import com.unipus.digitalbook.model.entity.clio.ClioOpenResult;
import com.unipus.digitalbook.model.entity.question.type.OralPersonalStateQuestion;
import com.unipus.digitalbook.model.enums.QuestionTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Set;

/**
 * 个人陈述题评测策略
 *
 * <AUTHOR>
 * @date 2025/3/21 14:41
 */
@Slf4j
@Component
public class OralPersonalStateJudgeStrategy extends AbstractOralJudgeStrategy<OralPersonalStateQuestion> {

    @Override
    public Set<QuestionTypeEnum> supportQuestionTypes() {
        return Set.of(QuestionTypeEnum.ORAL_PERSONAL_STATE);
    }

    @Override
    protected BigDecimal total(String evaluation) {
        ClioOpenResult clioOpenResult = JSON.parseObject(evaluation, ClioOpenResult.class);
        if (clioOpenResult != null && clioOpenResult.getFinalResult() != null
                && clioOpenResult.getFinalResult().getResult() != null
                && clioOpenResult.getFinalResult().getResult().getTotal() != null) {
            return BigDecimal.valueOf(clioOpenResult.getFinalResult().getResult().getTotal());
        }
        log.error("个人陈述作答结果数据异常: {}", evaluation);
        return BigDecimal.ZERO;
    }
}
