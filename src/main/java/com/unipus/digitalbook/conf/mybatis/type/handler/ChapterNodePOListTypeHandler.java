package com.unipus.digitalbook.conf.mybatis.type.handler;

import com.alibaba.fastjson2.JSON;
import com.unipus.digitalbook.model.po.chapter.ChapterNodePO;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;

@MappedTypes(List.class)
public class ChapterNodePOListType<PERSON>andler extends BaseTypeHandler<List<ChapterNodePO>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<ChapterNodePO> parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, JSON.toJSONString(parameter));
    }

    @Override
    public List<ChapterNodePO> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return parseJson(json);
    }

    @Override
    public List<ChapterNodePO> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return parseJson(json);
    }

    @Override
    public List<ChapterNodePO> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return parseJson(json);
    }

    private List<ChapterNodePO> parseJson(String json) {
        if (json == null || json.isEmpty()) {
            return Collections.emptyList();
        }
        return JSON.parseArray(json, ChapterNodePO.class);
    }


}
