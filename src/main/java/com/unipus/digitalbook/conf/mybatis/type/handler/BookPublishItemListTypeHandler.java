package com.unipus.digitalbook.conf.mybatis.type.handler;

import com.alibaba.fastjson2.JSON;
import com.unipus.digitalbook.model.po.book.BookPublishItem;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
@MappedTypes(List.class)
public class BookPublishItemListTypeHandler extends BaseTypeHandler<List<BookPublishItem>> {


    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<BookPublishItem> parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, JSON.toJSONString(parameter));
    }

    @Override
    public List<BookPublishItem> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return parseJson(json);
    }

    @Override
    public List<BookPublishItem> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return parseJson(json);
    }

    @Override
    public List<BookPublishItem> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return parseJson(json);
    }

    private List<BookPublishItem> parseJson(String json) {
        if (json == null || json.isEmpty()) {
            return Collections.emptyList();
        }
        return JSON.parseArray(json, BookPublishItem.class);
    }


}
