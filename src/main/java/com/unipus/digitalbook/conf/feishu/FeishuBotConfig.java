package com.unipus.digitalbook.conf.feishu;

import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * 飞书机器人配置类
 */
@Configuration
@EnableConfigurationProperties(FeishuBotProperties.class)
public class FeishuBotConfig {
    
    /**
     * 配置RestTemplate
     */
    @Bean
    public RestTemplate feishuRestTemplate(FeishuBotProperties feishuBotProperties) {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(feishuBotProperties.getTimeout());
        factory.setReadTimeout(feishuBotProperties.getTimeout());
        
        return new RestTemplate(factory);
    }
} 