package com.unipus.digitalbook.conf.security;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "white-list")
public class WhiteListProperties {

    /** 身份验证白名单 */
    private String[] securityUrls = new String[]{};

    /** 用户权限白名单 */
    private String[] permissionUrls = new String[]{};

    /** 内部调用白名单 */
    private String[] internalUrls = new String[]{};

    /** 内部IP地址网段 */
    private String[] internalIpRanges = new String[]{};

    public String[] getSecurityUrls() {
        return securityUrls;
    }

    public void setSecurityUrls(String[] securityUrls) {
        this.securityUrls = securityUrls;
    }

    public String[] getPermissionUrls() {
        return permissionUrls;
    }

    public void setPermissionUrls(String[] permissionUrls) {
        this.permissionUrls = permissionUrls;
    }

    public String[] getInternalUrls() {
        return internalUrls;
    }

    public void setInternalUrls(String[] internalUrls) {
        this.internalUrls = internalUrls;
    }

    public String[] getInternalIpRanges() {
        return internalIpRanges;
    }

    public void setInternalIpRanges(String[] internalIpRanges) {
        this.internalIpRanges = internalIpRanges;
    }
}
