package com.unipus.digitalbook.grpc;

import cn.unipus.qs.api.proto.client.model.QuesAddResponsePO;
import com.unipus.digitalbook.common.exception.question.GrpcQuestionInvalidResponseException;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 题目返回信息实体类
 */
@Schema(description = "题目返回信息实体类")
public class QuesGrpcAddResponse {

    @Schema(description = "题目id", requiredMode = Schema.RequiredMode.REQUIRED)
    private long id;

    @Schema(description = "题目版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private int version;

    @Schema(description = "返回信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private QuesGrpcStatus status;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public QuesGrpcStatus getStatus() {
        return status;
    }

    public void setStatus(QuesGrpcStatus status) {
        this.status = status;
    }

    public static QuesGrpcAddResponse fromGrpcResponse(QuesAddResponsePO response){
        if(response==null){
            throw new GrpcQuestionInvalidResponseException("response is null.(QuesAddResponsePO)");
        }
        QuesGrpcAddResponse res = new QuesGrpcAddResponse();
        res.setId(response.getId().getValue());
        res.setVersion(response.getVersion().getValue());
        res.setStatus(new QuesGrpcStatus(response.getStatus().getCode(), response.getStatus().getMsg()));
        return res;
    }

}