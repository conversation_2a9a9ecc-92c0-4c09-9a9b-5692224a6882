package com.unipus.digitalbook.grpc;

import cn.unipus.qs.api.proto.client.model.QuesDetailPO;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 题目详情
 */
public class QuesGrpcDetailResponse extends QuesGrpcDetailBase {

    @Schema(description = "题目类型", example = "1")
    private String correctType;

    @Schema(description = "题目ID(题库)", example = "1234567890")
    private Long quesId;

    @Schema(description = "题目版本", example = "1")
    private Integer version;

    @Schema(description = "题目状态", example = "1")
    private Integer status;

    @Schema(description = "子题目数量", example = "1")
    private Integer subQuesCount;

    @Schema(description = "创建时间", example = "1672531200000")
    private Long created;

    @Schema(description = "修改时间", example = "1672531200000")
    private Long modified;

    @Schema(description = "创建者", example = "user1")
    private String creator;

    @Schema(description = "修改者", example = "user2")
    private String modifier;

    @Schema(description = "组合类型", example = "1")
    private Integer combinationType;

    @Schema(description = "模板名称", example = "template1")
    private String templateName;

    public QuesGrpcDetailResponse() {
    }

    public String getCorrectType() {
        return correctType;
    }

    public void setCorrectType(String correctType) {
        this.correctType = correctType;
    }

    public Long getQuesId() {
        return quesId;
    }

    public void setQuesId(Long quesId) {
        this.quesId = quesId;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSubQuesCount() {
        return subQuesCount;
    }

    public void setSubQuesCount(Integer subQuesCount) {
        this.subQuesCount = subQuesCount;
    }

    public Long getCreated() {
        return created;
    }

    public void setCreated(Long created) {
        this.created = created;
    }

    public Long getModified() {
        return modified;
    }

    public void setModified(Long modified) {
        this.modified = modified;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Integer getCombinationType() {
        return combinationType;
    }

    public void setCombinationType(Integer combinationType) {
        this.combinationType = combinationType;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public static QuesGrpcDetailResponse build(QuesDetailPO quesDetailPO) {
        QuesGrpcDetailResponse response = new QuesGrpcDetailResponse();
        response.setQuesId(quesDetailPO.getId());
        response.setVersion(quesDetailPO.getVersion());
        response.setQuesTypeId(quesDetailPO.getQuesTypeId());
        response.setQuesTypeVersion(quesDetailPO.getQuesTypeVersion());
        response.setTitle(quesDetailPO.getTitle());
        response.setOwner(quesDetailPO.getOwner().getValue());
        response.setStatus(quesDetailPO.getStatus());
        response.setDescription(quesDetailPO.getDescription());
        response.setSubQuesCount(quesDetailPO.getSubQuesCount().getValue());
        response.setQuesDifficulty(quesDetailPO.getQuesDifficulty().getValue());
        response.setSourceId(quesDetailPO.getSourceId().getValue());
        response.setSourceSystemExternalId(quesDetailPO.getSourceExternalId());
        response.setSourceSystemExternalVersion(quesDetailPO.getSourceExternalVersion());
        response.setContent(quesDetailPO.getContent());
        response.setCreated(quesDetailPO.getCreated());
        response.setModified(quesDetailPO.getModified());
        response.setCombinationType(quesDetailPO.getCombinationType());
        response.setTemplateName(quesDetailPO.getTemplateName());
        response.setQuesYear(quesDetailPO.getYear());
        response.setShareStatus(quesDetailPO.getShareStatus());
        response.setSourceType(quesDetailPO.getSourceType());
        response.setBankId(quesDetailPO.getBankId());
        response.setSubject(quesDetailPO.getSubject());
        // quesDetailPO.getCorrectType();
        // quesDetailPO.getTagList());
        // quesDetailPO.getShortCode();

        return response;
    }

}

