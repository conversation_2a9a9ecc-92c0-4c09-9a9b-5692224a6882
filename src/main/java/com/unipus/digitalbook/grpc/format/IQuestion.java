package com.unipus.digitalbook.grpc.format;

import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.ChoiceQuestionOption;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.enums.QuestionBusinessTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

@Data
@Schema(description = "题目参数")
public class IQuestion {
    @Schema(description = "题目ID")
    private String id;
    @Schema(description = "题目说明信息")
    private Direction direction;
    @Schema(description = "题目内容列表")
    private List<ContentInfo> contents;
    @Schema(description = "题目文本")
    private String quesText;
    @Schema(description = "题目类型")
    private String type;
    @Schema(description = "题目类别")
    private Integer category;
    @Schema(description = "答题类型，如单选题等")
    private String replyType;
    @Schema(description = "题目选项列表")
    private List<OptionInfo> options;
    @Schema(description = "子题目列表")
    private List<IQuestion> children;
    @Schema(description = "题目答案列表")
    private List<String> answers;
    @Schema(description = "题目答案解析")
    private String analysis;
    @Schema(description = "题目规则信息")
    private Rule rule;

    public IQuestion() {}

    /**
     * 大题构造方法
     * @param questionGroup 大题参数
     */
    public IQuestion(BigQuestionGroup questionGroup) {
//        this.id = questionGroup.getId();
        // todo: 其他大题字段待确定
        this.direction = new Direction(questionGroup.getDirection(), questionGroup.getDirection());
        this.contents = List.of(new ContentInfo("", "", questionGroup.getContent()));

        String questionTypeName = QuestionBusinessTypeEnum.getNameByCode(questionGroup.getType());
        QuestionTypeRelation questionTypeEnum = QuestionTypeRelation.getEnumByName(questionTypeName);
        if(questionTypeEnum!=null) {
            this.type = questionTypeEnum.getQuestionType();
            this.replyType = questionTypeEnum.getQuestionReplyType();
            this.category = questionTypeEnum.getCategory().getValue();
        }

//        this.children = questionGroup.getQuestionList().stream().map(base->new IQuestion(base, questionTypeEnum)).toList();
    }

    /**
     * 小题构造方法
     * @param base 小题参数
     */
    public IQuestion(Question base, QuestionTypeRelation questionTypeEnum) {
        // 小题通用字段转换
//        this.id = base.getId();
//        this.quesText = base.getQuestionText();
//        this.analysis = base.getAnalysis();

        // 小题目类型转换
        if(questionTypeEnum!=null) {
            this.type = questionTypeEnum.getQuestionType();
            this.replyType = questionTypeEnum.getQuestionReplyType();
            this.category = questionTypeEnum.getCategory().getValue();
        }

        // 特定题目字段转换
//        this.options = base.getOptions() != null ? base.getOptions().stream().map(OptionInfo::new).toList() : List.of();
//        this.answers = base.getAnswers() != null ? base.getAnswers().stream().map(QuestionAnswer::getCorrectAnswer).toList() : List.of();

    }

    /**
     * 选项信息
     */
    @Data
    public static class OptionInfo {
        @Schema(description = "选项的值")
        private String value;
        @Schema(description = "选项名称，如 A、B、C、D")
        private String name;
        @Schema(description = "选项的文本内容")
        private String text;
        @Schema(description = "选项的图片路径")
        private String path;
        @Schema(description = "内容类型枚举")
        private ContentType type;

        public OptionInfo(ChoiceQuestionOption source) {
//            this.value = source.getValue();
            this.name = source.getName();
            this.text = source.getContent();
        }
    }

    /**
     * 音频设置
     */
    @Data
    public static class AudioSetting {
        @Schema(description = "音频播放方式，如点击播放、自动播放")
        private String playType;
        @Schema(description = "音频字幕显示时机，如作答前显示、作答后显示")
        private String subtitle;
        @Schema(description = "是否设置音频播放次数")
        private boolean setPlayNum;
        @Schema(description = "音频设置播放次数的值")
        private Integer playNum;
    }

    /**
     * 内容信息基类
     */
    @Data
    public static class ContentInfo {
        @Schema(description = "id")
        private String id;
        @Schema(description = "名称")
        private String name;
        @Schema(description = "标题，可选")
        private String title;
        @Schema(description = "类型，包括音频、视频、段落、句子、单词")
        private ContentType type;
        @Schema(description = "内容文本，富文本")
        private String text;
        @Schema(description = "相关的内容")
        private List<ContentInfo> relevance;
        @Schema(description = "是否挖空内容")
        private boolean isScoop;

        public ContentInfo() {}

        public ContentInfo(String id, String title, String text) {
            this.id = id;
            this.title = title;
            this.text = text;
        }
    }

    /**
     * 题目说明
     */
    @Data
    public static class Direction {
        @Schema(description = "说明文本")
        private String text;
        @Schema(description = "移动端说明文本")
        private String mText;
        @Schema(description = "PC 端说明文本")
        private String pcText;
        @Schema(description = "说明的标签")
        private String label;
        @Schema(description = "音频路径")
        private String audioPath;

        public Direction(String text, String htmlStr) {
            this.text = text;
            this.pcText = htmlStr;
        }
    }

    /**
     * 示例
     */
    @Data
    public static class Example {
        @Schema(description = "词性；名词、形容词")
        private String category;
        @Schema(description = "示例文本")
        private String text;
        @Schema(description = "示例路径：sentence 可能会有，指向某一 task.")
        private String path;
        @Schema(description = "发音文件地址")
        private String sound;
    }

    /**
     * 释义
     */
    @Data
    public static class Explanation {
        @Schema(description = "解释文本")
        private String text;
    }
//
//    /**
//     * 标签
//     */
//    @Data
//    public static class Label {
//        @Schema(description = "题目的难度星级")
//        private Integer quesDifficulty;
//        @Schema(description = "题目的知识点 ID 列表")
//        private List<String> knowledgePointIdList;
//
//        public Label(QuestionGroupParam.Label source) {
//            this.quesDifficulty = source.getQuesDifficulty();
//            this.knowledgePointIdList = source.getKnowledgePointIdList();
//        }
//    }

    /**
     * 音视频内容
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    @Schema(description = "Audio、Video内容")
    public static class MultiMedia extends ContentInfo {
        @Schema(description = "音视频介绍", maxLength = 1000)
        private String introduction;
        @Schema(description = "时长，单位：毫秒", maximum = "7200000")
        private int duration;
        @Schema(description = "大小，单位：字节")
        private long size;
        @Schema(description = "字幕文件地址（即将废弃）", maxLength = 100)
        private String vttPath;
        @Schema(description = "字幕文件地址", maxLength = 100, format = "uri/vtt")
        private String subtitlesPath;
        @Schema(description = "字幕文件名称", maxLength = 50)
        private String subtitlesName;
        @Schema(description = "字幕文件列表")
        private Subtitle[] subtitles;
        @Schema(description = "资源路径", maxLength = 100, format = "mp3,mp4/uri")
        private String path;
        @Schema(description = "资源路径（即将废弃）", maxLength = 100, format = "aac,m4a/uri")
        private String aacPath;
        @Schema(description = "打点信息")
        private Integer[] points;
        @Schema(description = "封面文件地址", maxLength = 100, format = "jpg,png/uri")
        private String imagePath;
        @Schema(description = "封面文件名称", maxLength = 100)
        private String imageName;
    }

    /**
     * 段落内容
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class Paragraph extends ContentInfo {
        @Schema(description = "音频文件地址", maxLength = 100, format = "mp3/uri")
        private String sound;
        @Schema(description = "音频文件名称", maxLength = 100)
        private String soundFile;
        @Schema(description = "音频时长，单位：秒", maximum = "7200")
        private Integer soundDuration;
        @Schema(description = "音频大小，单位：字节", maximum = "10000000")
        private Integer soundSize;
        @Schema(description = "段落释义")
        private List<Explanation> explanations;
        @Schema(description = "图片文件地址", maxLength = 100, format = "jpg,png/uri")
        private String imagePath;
        @Schema(description = "图片文件名称", maxLength = 100)
        private String imageName;
        @Schema(description = "段落中的句子和单词")
        private List<ContentInfo> children; // (Sentence | Word)
    }

    /**
     * 规则
     */
    @Data
    public static class Rule {
        @Schema(description = "规则的关键词列表")
        private List<String> keywords;
        @Schema(description = "规则文本")
        private String text;
        @Schema(description = "规则涉及的单词列表")
        private List<List<String>> words;
        private Integer lowerLimit;
        private Integer upperLimit;
    }

    /**
     * 句子内容
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class Sentence extends ContentInfo {
        @Schema(description = "音频文件地址", maxLength = 100, format = "mp3/uri")
        private String sound;
        @Schema(description = "音频文件名称", maxLength = 100)
        private String soundFile;
        @Schema(description = "音频时长，单位：秒", maximum = "7200")
        private Integer soundDuration;
        @Schema(description = "音频大小，单位：字节", maximum = "10000000")
        private Integer soundSize;
        @Schema(description = "句子释义")
        private List<Explanation> explanations;
        @Schema(description = "图片文件地址", maxLength = 100, format = "jpg,png/uri")
        private String imagePath;
        @Schema(description = "图片文件名称", maxLength = 100)
        private String imageName;
        @Schema(description = "句子文本类型")
        private TextType textType;
        @Schema(description = "打点信息")
        private Integer pointId;
        @Schema(description = "句子示例")
        private List<Example> examples;
        @Schema(description = "句子中的单词")
        private List<Word> children;
    }

    /**
     * 设置
     */
    @Data
    public static class Setting {
        @Schema(description = "答题方式，如拖拽、点选")
        private String answerType;
        @Schema(description = "是否自动判题")
        private boolean isJudgment;
        @Schema(description = "是否计分")
        private boolean isScoring;
        @Schema(description = "总作答时间，单位秒")
        private Integer totalAnswerTime;
        @Schema(description = "每道题的作答时间列表，单位秒")
        private List<Integer> answerTimeList;
        @Schema(description = "题目总分")
        private Integer totalScore;
        @Schema(description = "每个小题的分数列表")
        private List<Integer> fractionList;
        @Schema(description = "PC 端布局类型，如瀑布、分页、左右")
        private String pcLayoutType;
        @Schema(description = "APP 端布局类型，如瀑布、分页")
        private String appLayoutType;
        @Schema(description = "音频设置信息")
        private AudioSetting audioSetting;
        @Schema(description = "视频设置信息")
        private VideoSetting videoSetting;
        @Schema(description = "是否关联其他题目")
        private Boolean isConnect;
        @Schema(description = "关联的题目类型，如写作题、量表题、翻译题")
        private String connectQuestion;
        @Schema(description = "关联类型，如量表 + 写作展示等")
        private String connectType;
    }

    /**
     * 字幕
     */
    @Data
    public static class Subtitle {
        @Schema(description = "字幕名称")
        private String name;
        @Schema(description = "字幕路径")
        private String path;
    }

    /**
     * 视频设置
     */
    @Data
    public static class VideoSetting {
        @Schema(description = "视频播放方式，如点击播放、自动播放")
        private String playType;
        @Schema(description = "视频字幕显示时机，如作答前显示、作答后显示")
        private String subtitle;
    }

    /**
     * 单词内容
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class Word extends ContentInfo {
        @Schema(description = "发音文件地址", maxLength = 100, format = "mp3/uri")
        private String sound;
        @Schema(description = "发音文件名称", maxLength = 100)
        private String soundFile;
        @Schema(description = "发音音频时长，单位：秒", maximum = "7200")
        private Integer soundDuration;
        @Schema(description = "发音音频大小，单位：字节")
        private Integer soundSize;
        @Schema(description = "释义")
        private List<Explanation> explanations;
        @Schema(description = "图片文件地址,用于word背景", maxLength = 100, format = "jpg,png/uri")
        private String imagePath;
        @Schema(description = "图片文件名称", maxLength = 100)
        private String imageName;
        @Schema(description = "译文", maxLength = 1000)
        private String translation;
        @Schema(description = "句子文本类型；'title'：标题、'br': 换行、'sentence': 句子")
        private TextType textType;
        @Schema(description = "打点信息")
        private Integer pointId;
        @Schema(description = "例句")
        private List<Example> examples;
        @Schema(description = "英标", maxLength = 100)
        private String soundmark;
        @Schema(description = "词性，vi、vt", maxLength = 20)
        private String category;
    }

    /**
     * 内容类型枚举
     */
    @Getter
    @AllArgsConstructor
    public enum ContentType {
        AUDIO(1, "音频"),
        VIDEO(2, "视频"),
        IMAGE(3, "图片"),
        PARAGRAPH(4, "段落/文本"),
        SENTENCE(5, "句子"),
        WORD(6, "单词");

        private final int value;
        private final String description;
    }

    /**
     * 文本类型枚举
     */
    @Getter
    @RequiredArgsConstructor
    public enum TextType {
        TITLE("title", "标题"),
        BR("br", "换行"),
        SENTENCE("sentence", "句子");

        private final String value;
        private final String description;
    }
}

//
//    /**
//     */
//    @Getter
//    @AllArgsConstructor
//    protected enum QuestionType {
//        BASIC("basic", "基础题型 作答流程没有交互"),
//        BASIC_ORAL("basic-oral", "基础题型 作答流程没有交互 数据模型是Oral 口语题: 段落朗读 句子跟读"),
//        VIDEO_POINT_READ("video-point-read", "视频点读"),
//        VIDEO_POPUP("video-popup", "视频弹题"),
//        VOCABULARY("vocabulary", "单词本"),
//        RICH_TEXT_READ("rich-text-read", "精读课文"),
//        VIDEO_DUB("video-dub", "视频配音"),
//        AUDIO_LEARN("audio-learn", "音频学习"),
//        ORAL("oral", "个人陈述 简短回答（单题)"),
//        ORAL_STATE("oral-state", "口语陈述（单选、多选、简答）"),
//        FILLBLANK_SCOOP("fillblank-scoop", "挖空题(作答组件挖空)"),
//        BASIC_SCOOP_CONTENT("basic-scoop-content", "挖空题(材料区挖空)"),
//        MATERIAL_BANKED_CLOZE("material-banked-cloze", "挖空题(材料区挖空)"),
//        EXIT_TICKET("exit-ticket", "卡片题"),
//        MULTIPLE_ORAL("multiple-oral", "多小题口语题 简短回答（多小题）单小题"),
//        PAIR_WORK("pairwork", "国才中级-人人对话 两人讨论"),
//        PAIR_WORK_ONE_BY_ONE("pairwork-onebyone", "两人交替陈述_人工评阅"),
//        SELF_INTRODUCTION("self-introduction", "自我介绍"),
//        INTEGRATED_WRITING("integrated-writing", "综合写作"),
//        LISTENED_WRITING("listened-writing", "听后写作"),
//        CORRECTION("correction", "改错"),
//        ROLE_PLAY("role-play", "角色扮演"),
//        SEQUENCE("sequence", "排序"),
//        CONTENT_LEARN("content-learn", "内容学习（大纲、v3视频学习）"),
//        DISCUSSION("discussion", "讨论课"),
//        MULTI_FILE_UPLOAD("multiFileUpload", "多媒体上传题"),
//        PARAGRAPH_FOLLOW("paragraph-follow", "段落跟读"),
//        ORAL_ALOUD("oral-aloud", "短文朗读"),
//        ORAL_PERSONAL_STATE("oral-personal-state", "个人陈述"),
//        SHORT_ESSAY_RETELL("short-essay-retell", "短文复述"),
//        WORD_DICTATION("word-dictation", "单词听写");
//
//        private final String value;
//        private final String description;
//    }

//
//    /**
//     * 回答类型枚举
//     */
//    @Getter
//    @AllArgsConstructor
//    protected enum ReplyType {
//        DEFAULT("default", "default 兼容课程"),
//        NONE("", "none 兼容itest"),
//        SINGLE_CHOICE("singlechoice", "单选"),
//        MULTIPLE_CHOICE("multichoice", "多选"),
//        DROPDOWN_SELECTION("dropdownSelection", "下拉选择"),
//        FILL_BLANK("fillblank", "填空在basic题型中是单独input的作答组件，在挖空题中是fillblank组件"),
//        RECORD("record", "录音 句子跟读"),
//        RECORD_PARAGRAPH("recordParagraph", "段落跟读"),
//        ORAL_ALOUD("oralAloud", "短文朗读"),
//        BANKED_CLOZE("bankedcloze", "选词填空"),
//        ORAL_RETELL("oralRetell", "复述题"),
//        ORAL_PERSONAL_STATE("oral-personal-state", "个人陈述题"),
//        MULTI_FILE_UPLOAD("multiFileUpload", "多媒体上传题"),
//        TEXT_AREA("text-area", "写作、翻译、简答"),
//        ROLE_PLAY("role-play", "角色扮演"),
//        DISCUSSION("discussion", "讨论课"),
//        EXIT_TICKET("exit-ticket", "卡片题"),
//        TEXT_MATCH("text-match", "文本匹配"),
//        SEQUENCE("sequence", "排序"),
//        SENTENCE_SCOOP_RECORD("sentence-scoop-record", "挖空句子跟读"),
//        OUTLINE("outline", "大纲"),
//        SHORT_ANSWER_SCOOP("shortanswerScoop", "简答挖空"),
//        WORD_DICTATION("word-dictation", "单词听写");
//
//        private final String value;
//        private final String description;
//    }

