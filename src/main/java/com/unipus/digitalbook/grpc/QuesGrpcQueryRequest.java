package com.unipus.digitalbook.grpc;

import cn.unipus.qs.api.proto.client.model.IdVersionPO;
import cn.unipus.qs.api.proto.client.model.QuesDetailRequestPO;
import com.unipus.digitalbook.model.params.question.QuestionQueryParam;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * 题目查询参数实体类
 */
public class QuesGrpcQueryRequest {

    @Schema(description = "题目ID信息列表")
    private List<IdVersion> idVersion;

    @Schema(description = "是否包含题目内容")
    private Boolean includeQuestionContent;

    @Schema(description = "题目ID信息")
    public static class IdVersion {
        @Schema(description = "题目ID")
        private Long quesId;

        @Schema(description = "题目版本")
        private Integer version;

        // 构造函数
        public IdVersion(Long quesId, Integer version) {
            this.quesId = quesId;
            this.version = version;
        }

        // Getter 和 Setter 方法
        public Long getQuesId() {
            return quesId;
        }

        public void setQuesId(Long quesId) {
            this.quesId = quesId;
        }

        public Integer getVersion() {
            return version;
        }

        public void setVersion(Integer version) {
            this.version = version;
        }
    }

    // Getter 和 Setter 方法
    public List<IdVersion> getIdVersion() {
        return idVersion;
    }

    public void setIdVersion(List<IdVersion> idVersion) {
        this.idVersion = idVersion;
    }

    public Boolean getIncludeQuestionContent() {
        return includeQuestionContent;
    }

    public void setIncludeQuestionContent(Boolean includeQuestionContent) {
        this.includeQuestionContent = includeQuestionContent;
    }

    public static QuesDetailRequestPO fromQuestionQueryParam(QuestionQueryParam param) {
        return QuesDetailRequestPO.newBuilder()
                .setIncludeContent(param.getIncludeContent())
                .addAllIdVersion(param.getIdVersion().stream().map(idVersion -> IdVersionPO.newBuilder()
                        .setQuesId(idVersion.getQuesId())
                        .setVersion(idVersion.getVersion())
                        .build()
                ).toList())
                .build();
    }

}
