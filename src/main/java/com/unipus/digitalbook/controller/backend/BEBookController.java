package com.unipus.digitalbook.controller.backend;

import com.unipus.digitalbook.controller.BaseController;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.book.BookSimpleDataDTO;
import com.unipus.digitalbook.model.dto.book.BookSimpleDataListDTO;
import com.unipus.digitalbook.model.dto.book.BookStructDTO;
import com.unipus.digitalbook.model.dto.chapter.VersionListDTO;
import com.unipus.digitalbook.model.entity.BackendUserInfo;
import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.entity.book.Book;
import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.model.entity.chapter.ChapterList;
import com.unipus.digitalbook.model.entity.chapter.ChapterVersion;
import com.unipus.digitalbook.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/backend/book")
@Tag(name = "教材相关功能", description = "为了权限控制，该控制器特意未后端服务提供接口，包含教材相关的能力和数据")
public class BEBookController extends BaseController {

    @Resource
    private BookService bookService;
    @Resource
    UserService userService;
    @Resource
    private ChapterService chapterService;
    @Resource
    private PublishService publishService;

    @Resource
    private AuthService authService;

    @GetMapping("/getBookStruct")
    @Tag(name = "获取教材结构", description = "获取教材结构，根据教材id获取教材结构,如果不传versionNumber，则返回最新的书籍版本")
    @Operation(summary = "获取教材结构", description = "根据教材id获取教材结构,如果不传versionNumber，则返回最新的书籍版本")
    public Response<BookStructDTO> getBookStruct(@Schema(description = "教材唯一标识符", requiredMode = Schema.RequiredMode.REQUIRED) @RequestParam String bookId,
                                                 @Schema(description = "教材版本") @RequestParam(required = false) String versionNumber) {
        Book book = bookService.getBookByBookIdAndVersion(bookId, versionNumber);
        Map<Long, UserInfo> userMap = userService.getUserMap(List.of(book.getEditorId(), book.getCreateBy()));
        BookStructDTO structDTO = new BookStructDTO(book, userMap);
        //填充教材下的所有章节信息（包括所有上架和未上架的章节）
        ChapterList allChapterList = chapterService.getChaptersByBookId(book.getId());
        structDTO.setExpectedChapterListByEntity(allChapterList.getChapterList());
        structDTO.setChangeContentListByEntity(bookService.getBookChangeListByVersionId(book.getBookVersion().getId()));
        return Response.success(structDTO);
    }


    @GetMapping("/getChapterVersionByBookAndBookVersion")
    @Tag(name = "通过教材和教材版本，获取教材下所有章节版本信息", description = "通过教材和教材版本，获取教材下所有章节版本信息")
    @Operation(summary = "获取教材下所有章节版本信息", description = "根据教材id获取教材结构,如果不传versionNumber，则返回最新的书籍版本")
    public Response<VersionListDTO> getChapterVersionByBookAndBookVersion(@RequestParam String bookId, @RequestParam(required = false) String versionNumber) {
        Book book = bookService.getBookByBookIdAndVersion(bookId, versionNumber);
        if (book == null || CollectionUtils.isEmpty(book.getChapterList())) {
            return Response.success(new VersionListDTO());
        }
        List<Long> chapterVersionIdList = book.getChapterList().stream().map(Chapter::getChapterVersion).map(ChapterVersion::getId).toList();
        if (CollectionUtils.isEmpty(chapterVersionIdList)) {
            return Response.success(new VersionListDTO());
        }
        List<ChapterVersion> chapterVersions = chapterService.getChapterVersionByVersionIdList(chapterVersionIdList);
//        List<BookNode> bookNodes =
        if (CollectionUtils.isEmpty(chapterVersions)) {
            return Response.success(new VersionListDTO());
        }
        VersionListDTO versionListDTO = new VersionListDTO(chapterVersions);
        return Response.success(versionListDTO);

    }

    @GetMapping("/getChapterVersionByBookVersionId")
    @Tag(name = "根据教材版本ID,获取教材版本下的章节版本信息", description = "根据教材版本ID,获取教材版本下的章节版本信息")
    @Operation(summary = "根据教材版本ID,获取教材版本下的章节版本信息", description = "根据教材版本ID,获取教材版本下的章节版本信息")
    public Response<VersionListDTO> getChapterVersionByBookVersionId(@RequestParam Long bookVersionId) {
        List<Chapter> chapters = chapterService.getChapterListByBookVersionId(bookVersionId);
        if (CollectionUtils.isEmpty(chapters)) {
            return Response.success(new VersionListDTO());
        }
        List<ChapterVersion> chapterVersions = chapters.stream().map(Chapter::getChapterVersion).toList();
        return Response.success(new VersionListDTO(chapterVersions));
    }

    @GetMapping("/getBookSimpleDataList")
    @Tag(name = "获取全部教材的简要数据", description = "获取全部教材的简要数据")
    @Operation(summary = "获取全部教材的简要数据", description = "获取全部教材的简要数据")
    public Response<BookSimpleDataListDTO> getBookSimpleDataList() {
        // 解析登录 token，获取租户 ID
        String token = getAuthorizationHeader();
        Response<BackendUserInfo> response = authService.parseBackendAccessToken(token);
        if (!response.isSuccess()) {
            return Response.fail(response.getMessage());
        }

        Long tenantId = response.getData().getAppId();

        List<BookSimpleDataDTO> dataList = bookService.getBookSimpleDataList(tenantId);
        return Response.success(new BookSimpleDataListDTO(dataList));
    }

}
