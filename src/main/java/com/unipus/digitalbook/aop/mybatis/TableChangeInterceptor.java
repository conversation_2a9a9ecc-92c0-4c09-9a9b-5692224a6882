package com.unipus.digitalbook.aop.mybatis;

import com.unipus.digitalbook.common.utils.UserUtil;
import com.unipus.digitalbook.common.utils.VirtualThreadPoolManager;
import com.unipus.digitalbook.publisher.standalone.TableChangeEventPublisher;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.springframework.stereotype.Component;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * MyBatis拦截器，用于拦截数据库数据变更操作
 * 解析SQL中的表名，发送变更事件
 */
@Component
@Intercepts({
    @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})
})
@Slf4j
public class TableChangeInterceptor implements Interceptor {

    // 数据变更事件发布者
    @Resource
    private TableChangeEventPublisher eventPublisher;

    // 使用虚拟线程处理变更事件
    @Resource
    private VirtualThreadPoolManager virtualThreadPoolManager;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 获取方法参数
        Object[] args = invocation.getArgs();

        // 取得操作者ID
        String operator = getOperator();

        // 执行原始操作
        Object result = invocation.proceed();

        // 使用虚拟线程发送变更事件
        virtualThreadPoolManager.executeAsync(() -> sendTableChangeEvent(args, operator));

        // 返回原始操作结果
        return result;
    }

    /**
     * 发送表变更事件
     * @param args 数据库操作参数
     * @param operator 操作者ID
     */
    private void sendTableChangeEvent(Object[] args, String operator) {
        if (args == null || args.length < 2 || !(args[0] instanceof MappedStatement ms)) {
            log.error("Invalid arguments for table change event");
            return;
        }
        try {
            Object parameter = args[1];
            // 取得SQL语句
            String sql = ms.getBoundSql(parameter).getSql();
            // 设置操作类型
            String operationType = ms.getSqlCommandType().name();
            // 从 SQL 中提取表名（这里需要根据实际 SQL 解析来实现）
            String tableName = extractTableName(sql);

            // 发布事件
            eventPublisher.tableChangeEventPublisher("tableChangeInterceptor", tableName, operationType, parameter, sql, operator);
        } catch (Exception e) {
            log.error("Error sending table change event: {}", e.getMessage(), e);
        }
    }

    // 用于解析SQL中的表名的正则表达式(匹配关键字 + 匹配可选的开始单引号 + 捕获表名 + 匹配可选的结束单引号，忽略大小写)
    private static final Pattern TABLE_PATTERN = Pattern.compile(
            "(?:INSERT\\s+INTO|UPDATE|DELETE\\s+FROM)\\s+" + "`?" + "([a-zA-Z0-9_]+)" + "`?",
            Pattern.CASE_INSENSITIVE);

    /**
     * 从SQL语句中提取表名
     * @param sql SQL语句
     * @return 表名
     */
    private String extractTableName(String sql) {
        Matcher matcher = TABLE_PATTERN.matcher(sql);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return "unknown_table";
    }

    /**
     * 获取操作者
     * @return 操作者
     */
    private String getOperator() {
        try{
            return String.valueOf(UserUtil.getCurrentOperator());
        } catch (Exception e) {
            return "unknown_operator";
        }
    }
}
