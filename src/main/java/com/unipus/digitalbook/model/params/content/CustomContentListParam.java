package com.unipus.digitalbook.model.params.content;

import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "教材自建内容信息列表参数")
public class CustomContentListParam implements Params {

    @Schema(description = "教材自建内容信息列表")
    private List<CustomContentParam> customContentList;

    public List<CustomContentParam> getCustomContentList() {
        return customContentList;
    }

    public void setCustomContentList(List<CustomContentParam> customContentList) {
        this.customContentList = customContentList;
    }

    @Override
    public void valid() {

    }
}
