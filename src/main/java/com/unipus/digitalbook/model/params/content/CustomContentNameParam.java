package com.unipus.digitalbook.model.params.content;

import com.unipus.digitalbook.model.entity.content.CustomContent;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

@Schema(description = "教材自建内容名称信息参数")
public class CustomContentNameParam implements Params {

    @Schema(description = "内容业务ID")
    private String bizId;

    @Schema(description = "内容名称")
    private String name;

    @Schema(description = "内容数据包")
    private String contentPackage;

    public CustomContent toEntity(Long tenantId, Long userId) {
        CustomContent customContent = new CustomContent();
        customContent.setBizId(this.bizId);
        customContent.setName(this.name);
        customContent.setTenantId(tenantId);
        customContent.setUpdateBy(userId);
        return customContent;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContentPackage() {
        return contentPackage;
    }

    public void setContentPackage(String contentPackage) {
        this.contentPackage = contentPackage;
    }

    @Override
    public void valid() {
        if (StringUtils.isBlank(this.bizId)) {
            throw new IllegalArgumentException("bizId不能为空");
        }
        if (StringUtils.isBlank(this.name)) {
            throw new IllegalArgumentException("name不能为空");
        }
    }
}
