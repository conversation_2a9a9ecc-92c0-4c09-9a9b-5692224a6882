package com.unipus.digitalbook.model.params.content;

import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Schema(description = "发布自建内容信息参数")
public class PublishCustomContentParam implements Params {

    @Schema(description = "要发布的自建内容列表")
    private List<PublishContentItemParam> publishItemList;

    @Schema(description = "要删除的自建内容列表")
    private List<PublishContentItemParam> deleteItemList;

    public List<PublishContentItemParam> getPublishItemList() {
        return publishItemList;
    }

    public void setPublishItemList(List<PublishContentItemParam> publishItemList) {
        this.publishItemList = publishItemList;
    }

    public List<PublishContentItemParam> getDeleteItemList() {
        return deleteItemList;
    }

    public void setDeleteItemList(List<PublishContentItemParam> deleteItemList) {
        this.deleteItemList = deleteItemList;
    }

    @Override
    public void valid() {
        // 发布和删除列表不能都为空
        if (CollectionUtils.isEmpty(publishItemList) && CollectionUtils.isEmpty(deleteItemList)) {
            throw new IllegalArgumentException("发布内容列表和删除内容列表不能都为空");
        }
        // 校验发布内容列表
        if (!CollectionUtils.isEmpty(publishItemList)) {
            for (PublishContentItemParam item : publishItemList) {
                item.valid();
            }
        }
        // 校验删除内容列表
        if (!CollectionUtils.isEmpty(deleteItemList)) {
            for (PublishContentItemParam item : deleteItemList) {
                item.valid();
            }
        }
    }
}
