package com.unipus.digitalbook.model.params.paper.question;

import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import com.unipus.digitalbook.model.params.Params;
import com.unipus.digitalbook.model.params.question.QuestionSettingParam;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.IntStream;

@Schema(description = "试卷大题题组参数")
public class PaperBigQuestionGroupParam implements Params {

    @Schema(description = "题组ID")
    private String groupId;
    @Schema(description = "题组类型", example = "single_choice_group")
    private String groupType;
    @Schema(description = "题组说明信息")
    private String direction;
    @Schema(description = "题组内容列表")
    private String material;
    @Schema(description = "难度星级 1-5")
    private int groupDifficulty;
    @Schema(description = "题目列表")
    private List<PaperQuestionBaseParam> list;
    @Schema(description = "题组答案解析")
    private String analysis;
    @Schema(description = "题组设置信息，如答题方式、计分等")
    private QuestionSettingParam groupSetting;

    @Override
    public void valid() {
        if (!StringUtils.hasText(groupId)) {
            throw new IllegalArgumentException("大题ID不能为空");
        }
        if (!StringUtils.hasText(groupType)) {
            throw new IllegalArgumentException("大题类型不能为空");
        }
        if (CollectionUtils.isEmpty(list)) {
            throw new IllegalArgumentException("大题说明不能为空");
        }
        list.forEach(PaperQuestionBaseParam::valid);
    }

    public BigQuestionGroup toEntity(Long editQuestionUserId, String versionNumber) {

        // 转换基本信息
        BigQuestionGroup questionGroup = this.baseToEntity(editQuestionUserId, versionNumber);
        if (CollectionUtils.isEmpty(this.getList())) {
            return questionGroup;
        }

        // 试卷题目列表初始化
        List<PaperQuestionBaseParam> tmpList = new ArrayList<>(this.getList());
        // 追加推荐题列表
        List<PaperQuestionBaseParam> recommends = this.getList().stream()
                .map(PaperQuestionBaseParam::getRecommends)
                .filter(rs -> !CollectionUtils.isEmpty(rs))
                .flatMap(List::stream)
                .toList();
        if(!CollectionUtils.isEmpty(recommends)){
            tmpList.addAll(recommends);
        }
        // 转换题目信息
        List<Question> questions = IntStream.range(0, tmpList.size())
                .mapToObj(i -> {
                    PaperQuestionBaseParam param = tmpList.get(i);
                    Question entity = param.toEntity(editQuestionUserId);
                    entity.setSortOrder(i);
                    return entity;
                }).toList();

        questionGroup.setQuestions(questions);
        return questionGroup;
    }

    public BigQuestionGroup baseToEntity(Long editQuestionUserId, String versionNumber) {
        BigQuestionGroup questionGroup = new BigQuestionGroup();
        // 设置题组父ID
        // 版本
        questionGroup.setVersionNumber(versionNumber);
        // 设置题组ID
        questionGroup.setBizGroupId(this.getGroupId());
        // 设置题型
        questionGroup.setType(QuestionGroupTypeEnum.getCodeByName(this.getGroupType()));
        // 设置作答提示
        questionGroup.setDirection(this.getDirection());
        // 设置材料内容
        questionGroup.setContent(this.getMaterial());
        // 设置答案解析
        questionGroup.setAnalysis(this.getAnalysis());
        // 设置题组难度级别
        questionGroup.setDifficulty(this.getGroupDifficulty());
        questionGroup.setEnable(true);
        questionGroup.setCreateBy(editQuestionUserId);
        questionGroup.setUpdateBy(editQuestionUserId);
        questionGroup.setSetting(Optional.ofNullable(this.getGroupSetting())
                .map(p -> p.toEntity(editQuestionUserId)).orElse(null));
        List<PaperQuestionBaseParam> tmpList = this.getList();
        List<Question> questions = IntStream.range(0, tmpList.size())
                .mapToObj(i -> {
                    PaperQuestionBaseParam param = tmpList.get(i);
                    Question entity = param.toEntity(editQuestionUserId);
                    entity.setSortOrder(i);
                    return entity;
                }).toList();
        questionGroup.setScore(questions.stream().map(Question::getScore)
                .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        questionGroup.setQuestions(questions);
        questionGroup.setSortOrder(0);
        return questionGroup;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getGroupType() {
        return groupType;
    }

    public void setGroupType(String groupType) {
        this.groupType = groupType;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    public int getGroupDifficulty() {
        return groupDifficulty;
    }

    public void setGroupDifficulty(int groupDifficulty) {
        this.groupDifficulty = groupDifficulty;
    }

    public List<PaperQuestionBaseParam> getList() {
        return list;
    }

    public void setList(List<PaperQuestionBaseParam> list) {
        this.list = list;
    }

    public String getAnalysis() {
        return analysis;
    }

    public void setAnalysis(String analysis) {
        this.analysis = analysis;
    }

    public QuestionSettingParam getGroupSetting() {
        return groupSetting;
    }

    public void setGroupSetting(QuestionSettingParam groupSetting) {
        this.groupSetting = groupSetting;
    }
}
