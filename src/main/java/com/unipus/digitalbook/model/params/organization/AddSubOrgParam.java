package com.unipus.digitalbook.model.params.organization;

import com.unipus.digitalbook.model.entity.Organization;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "添加子组织参数")
public class AddSubOrgParam implements Params {
    @Schema(description = "组织名称", example = "一个组织名称")
    String name;
    @Schema(description = "组织类型", example = "1:集团、2:公司、3:部门、4:学校")
    Integer organizationType;
    @Schema(description = "上级组织id", example = "1234567")
    Long parentId;
    @Schema(description = "组织状态", example = "0:禁用、1:启用")
    Integer status;

    @Override
    public void valid() {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("请输入机构名称");
        }
        if (name.length() > 50) {
            throw new IllegalArgumentException("组织名称最大可输入50个字符");
        }
        if (organizationType == null) {
            throw new IllegalArgumentException("请选择状态组织类型");
        }
        if (status == null) {
            throw new IllegalArgumentException("请选择状态");
        }
        if (parentId == null){
            throw new IllegalArgumentException("上级组织ID不能为空");
        }
        if (parentId < 0) {
            throw new IllegalArgumentException("上级组织ID不能为负数");
        }
    }

    public String getName() {
        return name;
    }

    public AddSubOrgParam setName(String name) {
        this.name = name;
        return this;
    }

    public Integer getOrganizationType() {
        return organizationType;
    }

    public AddSubOrgParam setOrganizationType(Integer organizationType) {
        this.organizationType = organizationType;
        return this;
    }

    public Long getParentId() {
        return parentId;
    }

    public AddSubOrgParam setParentId(Long parentId) {
        this.parentId = parentId;
        return this;
    }


    public Organization toEntity() {
        Organization organization = new Organization();
        organization.setOrgName(this.getName());
        organization.setOrgType(this.getOrganizationType());
        organization.setParentId(this.getParentId());
        organization.setStatus(this.getStatus());
        // 其他字段可以根据需要进行设置，这里假设其他字段在创建时不需要立即设置
        return organization;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
