package com.unipus.digitalbook.model.params.book;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.StringJoiner;

@Schema(description = "批量添加教材上传文件参数")
public class BatchAddBookFileParam {
    List<AddBookFileParam> bookFileList;

    public List<AddBookFileParam> getBookFileList() {
        return bookFileList;
    }

    public void setBookFileList(List<AddBookFileParam> bookFileList) {
        this.bookFileList = bookFileList;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", BatchAddBookFileParam.class.getSimpleName() + "[", "]")
                .add("bookFileList=" + bookFileList)
                .toString();
    }
}
