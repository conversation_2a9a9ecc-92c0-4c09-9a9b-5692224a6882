package com.unipus.digitalbook.model.params.template;

import com.unipus.digitalbook.model.entity.template.PaperScoreTemplateRelation;
import com.unipus.digitalbook.model.enums.PaperScoreTemplateTypeEnum;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

@Data
@Schema(description = "新增教材关联模板参数")
public class AddPaperScoreTemplateRelationParam implements Params {

    @Schema(description = "教材Id")
    private String bookId;


    @Schema(description = "模板Id")
    private Long paperScoreTemplateId;

    /**
     * {@link PaperScoreTemplateTypeEnum}
     */
    @Schema(description = "模板类型，1:挑战评价,2:诊断评价")
    private Integer paperScoreTemplateType;


    @Override
    public void valid() {
        // 创建一个新的ValidationResult实例，用于存储验证结果
        ValidationResult result = new ValidationResult();

        if (StringUtils.isBlank(this.getBookId())) {
            result.addError("教材Id", "请选择教材");
        }

        if (ObjectUtils.isEmpty(this.getPaperScoreTemplateId())) {
            result.addError("模板Id", "请选择模板");
        }

        if (ObjectUtils.isEmpty(this.getPaperScoreTemplateType())) {
            result.addError("模板类型", "请选择模板类型");
        } else if (PaperScoreTemplateTypeEnum.getByCode(this.getPaperScoreTemplateType()).isEmpty()) {
            result.addError("模板类型", "模板类型错误");
        }

        if (result.hasErrors()) {
            throw new IllegalArgumentException(result.getErrorMessage());
        }
    }

    public PaperScoreTemplateRelation toEntity() {
        PaperScoreTemplateRelation paperScoreTemplateRelation = new PaperScoreTemplateRelation();
        paperScoreTemplateRelation.setBookId(this.getBookId());
        paperScoreTemplateRelation.setPaperScoreTemplateId(this.getPaperScoreTemplateId());
        paperScoreTemplateRelation.setPaperScoreTemplateType(this.getPaperScoreTemplateType());
        return paperScoreTemplateRelation;
    }
}
