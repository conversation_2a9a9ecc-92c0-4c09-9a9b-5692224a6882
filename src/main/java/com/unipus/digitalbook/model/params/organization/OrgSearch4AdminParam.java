package com.unipus.digitalbook.model.params.organization;

import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.StringJoiner;

@Schema(description = "查询机构入参")
public class OrgSearch4AdminParam implements Params {

    @Schema(description = "机构名称")
    private String name;

    @Schema(description = "机构状态")
    private Integer status;

    @Schema(description = "分页参数")
    private PageParams pageParams;

    public String getName() {
        return name;
    }

    public OrgSearch4AdminParam setName(String name) {
        this.name = name;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public OrgSearch4AdminParam setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public PageParams getPageParams() {
        return pageParams;
    }

    public void setPageParams(PageParams pageParams) {
        this.pageParams = pageParams;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrgSearch4AdminParam.class.getSimpleName() + "[", "]")
                .add("name='" + name + "'")
                .add("status=" + status)
                .toString();
    }

    @Override
    public void valid() {

    }
}
