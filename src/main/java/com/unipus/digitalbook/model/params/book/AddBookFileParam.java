package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.entity.book.BookFile;
import io.swagger.v3.oas.annotations.media.Schema;


/**
 * 添加书籍文件参数类
 */
@Schema(description = "添加书籍文件的请求参数")
public class AddBookFileParam {

    /**
     * 关联书籍的ID
     */
    @Schema(description = "关联书籍的唯一标识符", example = "xxx-zzzz", requiredMode = Schema.RequiredMode.REQUIRED )
    private String bookId;

    /**
     * 资源名称，例如：原始文件名或用户自定义名称
     */
    @Schema(description = "资源名称（例如：原始文件名或用户自定义名称）", example = "example.pdf")
    private String resourceName;

    /**
     * 资源的存储路径或URL
     */
    @Schema(description = "资源的存储路径或URL", example = "https://example.com/example.pdf")
    private String storagePath;

    /**
     * 资源类型，例如：IMAGE, AUDIO, VIDEO, DOCUMENT
     */
    @Schema(description = "资源类型，例如：IMAGE, AUDIO, VIDEO, DOCUMENT", example = "DOCUMENT")
    private String resourceType;

    /**
     * MIME类型，例如：image/jpeg, audio/mpeg, application/pdf
     */
    @Schema(description = "MIME类型，例如：image/jpeg, audio/mpeg, application/pdf", example = "application/pdf")
    private String mimeType;

    /**
     * 文件大小（字节）
     */
    @Schema(description = "文件大小（字节）", example = "123456")
    private Long fileSizeBytes;

    /**
     * 上传者用户ID，如果需要追踪上传者，可以关联用户表
     */
    @Schema(description = "上传者用户ID，如果需要追踪上传者，可以关联用户表", example = "12345")
    private Long uploaderId;

    /**
     * 资源状态，例如：ACTIVE, DELETED, PENDING
     */
    @Schema(description = "资源状态，例如：ACTIVE, DELETED, PENDING", example = "ACTIVE")
    private String status;

    /**
     * 资源描述或备注（可选）
     */
    @Schema(description = "资源描述或备注（可选）", example = "这是一个示例文档")
    private String description;

    /**
     * 将AddBookFileParam对象转换为BookFile实体对象
     *
     * @return BookFile 实体对象
     */
    public BookFile toEntity() {
        BookFile bookFile = new BookFile();
        bookFile.setBookId(this.bookId);
        bookFile.setResourceName(this.resourceName);
        bookFile.setStoragePath(this.storagePath);
        bookFile.setResourceType(this.resourceType);
        bookFile.setMimeType(this.mimeType);
        bookFile.setFileSizeBytes(this.fileSizeBytes);
        bookFile.setUploaderId(this.uploaderId);
        bookFile.setStatus(this.status);
        bookFile.setDescription(this.description);

        return bookFile;
    }

    // Getters and Setters
    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public String getStoragePath() {
        return storagePath;
    }

    public void setStoragePath(String storagePath) {
        this.storagePath = storagePath;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    public Long getFileSizeBytes() {
        return fileSizeBytes;
    }

    public void setFileSizeBytes(Long fileSizeBytes) {
        this.fileSizeBytes = fileSizeBytes;
    }

    public Long getUploaderId() {
        return uploaderId;
    }

    public void setUploaderId(Long uploaderId) {
        this.uploaderId = uploaderId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
