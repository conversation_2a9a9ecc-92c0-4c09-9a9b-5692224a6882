package com.unipus.digitalbook.model.params.complement;

import com.unipus.digitalbook.model.entity.complement.ComplementResourceReference;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

@Schema(description = "配套资源引用追加参数")
public class AddComplementResourceReferenceParam implements Params {

    @Schema(description = "配套资源ID")
    private String complementResourceId;

    @Schema(description = "配套资源引用位置")
    private String position;

    @Schema(description = "章节id")
    private String chapterId;

    public String getComplementResourceId() {
        return complementResourceId;
    }

    public void setComplementResourceId(String complementResourceId) {
        this.complementResourceId = complementResourceId;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    // Getter and Setter methods
    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    @Override
    public void valid() {
        if (!StringUtils.hasText(this.complementResourceId)) {
            throw new IllegalArgumentException("配套资源ID");
        }
        if (!StringUtils.hasText(this.position)) {
            throw new IllegalArgumentException("配套资源引用位置不能为空");
        }
        if (!StringUtils.hasText(this.chapterId)) {
            throw new IllegalArgumentException("章节id不能为空");
        }
    }

    public ComplementResourceReference toEntity() {
        ComplementResourceReference entity = new ComplementResourceReference();
        entity.setId(null);
        entity.setComplementResourceId(this.complementResourceId);
        entity.setPosition(this.position);
        entity.setChapterId(this.chapterId);
        entity.setEnable(true);
        return entity;
    }
}
