package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.enums.QuestionTypeEnum;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

@Schema(description = "用户答题参数")
public class UserAnswerParam implements Params {
    @Schema(description = "题类型")
    private String questionType;
    @Schema(description = "子题目id")
    private String childId;
    @Schema(description = "用户回答")
    private String userAnswerValue;
    @Schema(description = "用户答题id, 异步作答题型提交分数、获取结果的必要条件")
    private String bizAnswerId;

    @Override
    public void valid() {
        if (!StringUtils.hasText(this.questionType)) {
            throw new IllegalArgumentException("题类型不能为空");
        }
        if (!StringUtils.hasText(this.childId)) {
            throw new IllegalArgumentException("子题目id不能为空");
        }
    }

    public UserAnswer toEntity(Long tenantId, String envPartition, String openId, String versionNumber, String batchId) {
        UserAnswer userAnswer = toEntity(openId, versionNumber, batchId);
        userAnswer.setTenantId(tenantId);
        userAnswer.setEnvPartition(envPartition);
        return userAnswer;
    }

    public UserAnswer toEntity(String openId, String versionNumber, String batchId) {
        UserAnswer userAnswer = new UserAnswer();
        userAnswer.setBizQuestionId(this.childId);
        userAnswer.setBizAnswerId(this.getBizAnswerId());
        userAnswer.setQuestionVersionNumber(versionNumber);
        userAnswer.setQuestionType(QuestionTypeEnum.getCodeByName(this.questionType));
        userAnswer.setAnswer(this.userAnswerValue);
        userAnswer.setOpenId(openId);
        userAnswer.setBatchId(batchId);
        userAnswer.setStatus(0);
        return userAnswer;
    }

    public String getChildId() {
        return childId;
    }

    public void setChildId(String childId) {
        this.childId = childId;
    }

    public String getUserAnswerValue() {
        return userAnswerValue;
    }

    public void setUserAnswerValue(String userAnswerValue) {
        this.userAnswerValue = userAnswerValue;
    }

    public String getQuestionType() {
        return questionType;
    }

    public void setQuestionType(String questionType) {
        this.questionType = questionType;
    }

    public String getBizAnswerId() {
        return bizAnswerId;
    }

    public void setBizAnswerId(String bizAnswerId) {
        this.bizAnswerId = bizAnswerId;
    }

}
