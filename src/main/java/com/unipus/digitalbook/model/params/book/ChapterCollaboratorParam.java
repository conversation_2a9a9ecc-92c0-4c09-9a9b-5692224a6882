package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * 章节协作者参数
 */
@Schema(description = "章节协作者参数")
public class ChapterCollaboratorParam implements Params {

    @Schema(description = "章节ID")
    private List<String> chapterIds;

    public List<String> getChapterIds() {
        return chapterIds;
    }

    public void setChapterIds(List<String> chapterIds) {
        this.chapterIds = chapterIds;
    }

    @Override
    public void valid() {
        if (chapterIds == null || chapterIds.isEmpty()) {
            throw new IllegalArgumentException("章节ID不能为空");
        }
    }
}
