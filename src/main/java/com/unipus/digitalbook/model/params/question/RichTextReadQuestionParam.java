package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.RichTextReadQuestion;

/**
 * 精读课文题参数
 */
public class RichTextReadQuestionParam extends QuestionBaseParam {


    @Override
    public void valid() {

    }

    @Override
    protected Question toQuestion(QuestionText questionText) {
        RichTextReadQuestion richTextReadQuestion = new RichTextReadQuestion();
        richTextReadQuestion.setQuestionText(new QuestionText(getQuesText(), getQuesTextString()));
        return richTextReadQuestion;
    }
}
