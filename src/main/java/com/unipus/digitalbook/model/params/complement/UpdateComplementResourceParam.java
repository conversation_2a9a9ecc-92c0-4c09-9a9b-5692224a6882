package com.unipus.digitalbook.model.params.complement;

import com.unipus.digitalbook.model.entity.complement.ComplementResourceReference;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "配套资源引用更新参数")
public class UpdateComplementResourceParam implements Params {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "配套资源ID")
    private String complementResourceId;

    @Schema(description = "配套资源引用位置")
    private String position;

    @Schema(description = "章节id")
    private String chapterId;

    // Getter and Setter methods

    public Long getId() {
        return id;
    }
    public void setId(Long id) {
        this.id = id;
    }

    public String getComplementResourceId() {
        return complementResourceId;
    }

    public void setComplementResourceId(String complementResourceId) {
        this.complementResourceId = complementResourceId;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    @Override
    public void valid() {
        if(id == null) {
            throw new IllegalArgumentException("id不能为空");
        }
    }

    public ComplementResourceReference toEntity() {
        ComplementResourceReference entity = new ComplementResourceReference();
        entity.setId(this.id);
        entity.setComplementResourceId(this.complementResourceId);
        entity.setPosition(this.position);
        entity.setChapterId(this.chapterId);
        entity.setEnable(true);
        return entity;
    }
}
