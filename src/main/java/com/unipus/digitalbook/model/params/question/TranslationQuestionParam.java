package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.TranslationQuestion;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 写作题题
 */
public class TranslationQuestionParam extends QuestionBaseParam {

    @Override
    public void valid() {
        if (CollectionUtils.isEmpty(getChildren())) {
           if (CollectionUtils.isEmpty(getAnswers())) {
               throw new IllegalArgumentException("答案不能为空");
           }
        } else {
            if (!StringUtils.hasText(getQuesText())) {
                throw new IllegalArgumentException("题干不能为空");
            }
        }
    }

    @Override
    protected Question toQuestion(QuestionText questionText) {
        QuestionText currentQuestionText = new QuestionText(questionText.getText(), questionText.getPlainText());
        currentQuestionText.setAnswerWordLimit(getAnswerWordLimit());
        currentQuestionText.setEvaluationText(questionText.getEvaluationText());
        TranslationQuestion question = new TranslationQuestion();
        question.setQuestionText(currentQuestionText);
        return question;
    }
}
