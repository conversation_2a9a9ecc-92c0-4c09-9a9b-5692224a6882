package com.unipus.digitalbook.model.params.complement;

import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "更新资源的名字参数")
public class UpdateResourceNameParam implements Params {
    @Schema(description = "资源id", example = "1")
    private String id;
    @Schema(description = "资源名称", example = "资源名称")
    private String name;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public void valid() {
        if (id == null || id.isEmpty()) {
            throw new IllegalArgumentException("id不能为空");
        }
        if (name == null || name.isEmpty()) {
            throw new IllegalArgumentException("名称不能为空");
        }
        if (name.length() > 100) {
            throw new IllegalArgumentException("名称最长可输入100个字符");
        }
    }
}
