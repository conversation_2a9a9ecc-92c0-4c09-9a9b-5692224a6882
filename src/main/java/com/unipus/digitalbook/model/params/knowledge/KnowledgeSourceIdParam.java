package com.unipus.digitalbook.model.params.knowledge;

import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025年06月05日 18:10
 */
@Data
public class KnowledgeSourceIdParam implements Params {

    /**
     * 课程资源主键
     */
    private Long id;

    private static final long serialVersionUID = 1L;

    @Override
    public void valid() {
        // 创建一个新的ValidationResult实例，用于存储验证结果
        ValidationResult result = new ValidationResult();
        if (null == this.getId()) {
            result.addError("教材Id主键为空", "请输入教材Id主键");
        }

        // 教材用途区域验证
        if (result.hasErrors()) {
            throw new IllegalArgumentException(result.getFirstErrorMessage());
        }
    }
}
