package com.unipus.digitalbook.model.params.template;

import com.unipus.digitalbook.model.entity.template.PaperScoreTemplateDetail;
import com.unipus.digitalbook.model.enums.PaperScoreTemplateDEPTypeEnum;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

@Data
@Schema(description = "新增模板详情参数")
public class AddPaperScoreTemplateDetailParam implements Params {

    @Schema(description = "最低分")
    private Integer minScore;

    @Schema(description = "最高分")
    private Integer maxScore;

    /**
     * {@link PaperScoreTemplateDEPTypeEnum}
     */
    @Schema(description = "评价短语, 1-恭喜 2-好样 3-加油")
    private Integer evaluatePhrases;

    @Schema(description = "激励文案/评价文案")
    private String evaluateText;

    @Override
    public void valid() {
        // 创建一个新的ValidationResult实例，用于存储验证结果
        ValidationResult result = new ValidationResult();

        // 最低分参数校验
        if (ObjectUtils.isEmpty(this.getMinScore())) {
            result.addError("最低分", "请输入最低分");
        } else if (this.getMinScore() < 0) {
            result.addError("最低分", "最小可输入0");
        } else if (this.getMinScore() >= 100) {
            result.addError("最低分", "最大可输入99");
        }

        // 最高分参数校验
        if (ObjectUtils.isEmpty(this.getMaxScore())) {
            result.addError("最高分", "请输入最高分");
        } else if (this.getMaxScore() <= 0) {
            result.addError("最高分", "最小可输入1");
        } else if (this.getMaxScore() > 100) {
            result.addError("最高分", "最大可输入100");
        } else if (this.getMinScore() >= this.getMaxScore()){
            result.addError("最高分", "必须大于最低分");
        }

        // 评价短语参数校验
        if (ObjectUtils.isNotEmpty(this.getEvaluatePhrases())
                && PaperScoreTemplateDEPTypeEnum.getByCode(this.getEvaluatePhrases()).isEmpty()) {
                result.addError("评价短语", "评价短语数据类型错误");
            }

        // 评价文案参数校验
        if (StringUtils.isBlank(this.getEvaluateText())) {
            result.addError("评价文案", "请输入评价文案");
        }

        if (result.hasErrors()) {
            throw new IllegalArgumentException(result.getErrorMessage());
        }

    }

    public PaperScoreTemplateDetail toEntity() {
        PaperScoreTemplateDetail paperScoreTemplateDetail = new PaperScoreTemplateDetail();
        paperScoreTemplateDetail.setMinScore(this.getMinScore());
        paperScoreTemplateDetail.setMaxScore(this.getMaxScore());
        paperScoreTemplateDetail.setEvaluatePhrases(this.getEvaluatePhrases());
        paperScoreTemplateDetail.setEvaluateText(this.getEvaluateText());
        return paperScoreTemplateDetail;
    }

}
