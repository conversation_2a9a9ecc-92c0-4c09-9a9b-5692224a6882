package com.unipus.digitalbook.model.params.complement;

import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "查询资源参数")
public class ComplementSearchParam implements Params {
    @Schema(description = "教材ID")
    private String bookId;

    @Schema(description = "资源类型")
    private Integer resourceType;

    @Schema(description = "可见状态 1仅老师可见")
    private Integer visibleStatus;
    @Schema(description = "分页参数")
    private PageParams pageParams;

    public Integer getResourceType() {
        return resourceType;
    }

    public void setResourceType(Integer resourceType) {
        this.resourceType = resourceType;
    }

    public PageParams getPageParams() {
        return pageParams;
    }

    public void setPageParams(PageParams pageParams) {
        this.pageParams = pageParams;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public Integer getVisibleStatus() {
        return visibleStatus;
    }

    public void setVisibleStatus(Integer visibleStatus) {
        this.visibleStatus = visibleStatus;
    }

    @Override
    public void valid() {
        if (bookId == null || bookId.isEmpty()) {
            throw new IllegalArgumentException("bookId不能为空");
        }
    }
}
