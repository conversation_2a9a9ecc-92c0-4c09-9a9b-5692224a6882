package com.unipus.digitalbook.model.params.knowledge;

import com.alibaba.excel.util.StringUtils;
import com.unipus.digitalbook.model.params.Params;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年06月05日 18:10
 */
@Data
public class KnowledgeSourceDeleteByThirdIdsParam implements Params {

    private String knowledgeId;

    List<Long> thirdResourceIds;

    @Override
    public void valid() {
        // 创建一个新的ValidationResult实例，用于存储验证结果
        ValidationResult result = new ValidationResult();

        if (StringUtils.isEmpty(this.getKnowledgeId())) {
            result.addError("知识图谱的Id为空", "请输入知识图谱的id");
        }

        if (null == this.getThirdResourceIds()) {
            result.addError("知识图谱的资源Ids为空", "请输入知识图谱的资源Ids为空");
        }
        // 教材用途区域验证
        if (result.hasErrors()) {
            throw new IllegalArgumentException(result.getFirstErrorMessage());
        }
    }
}
