package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "批量提交参数")
public class ChapterQuestionGroupParam {

    @Schema(description = "大题组")
    private List<BigQuestionGroupParam> bigQuestions;

    @Schema(description = "章节id")
    private String chapterId;

    public List<BigQuestionGroup> toEntities(Long userId, Long parentId) {
        return bigQuestions.stream().map(param -> {
            BigQuestionGroup entity = param.toEntity(userId, null);
            entity.setParentId(parentId);
            return entity;
        }).toList();
    }
    public List<BigQuestionGroupParam> getBigQuestions() {
        return bigQuestions;
    }

    public void setBigQuestions(List<BigQuestionGroupParam> bigQuestions) {
        this.bigQuestions = bigQuestions;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }
}
