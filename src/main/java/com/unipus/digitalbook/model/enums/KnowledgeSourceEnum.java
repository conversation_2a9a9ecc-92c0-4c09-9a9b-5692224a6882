package com.unipus.digitalbook.model.enums;

public enum KnowledgeSourceEnum {
    IPUBLISH("1", "IPublish"),
    CMS("2", "CMS"),
    UAI("3", "UAI"),
    ;
    private final String code;
    private final String description;

    KnowledgeSourceEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static KnowledgeSourceEnum getStatus(String code) {
        for (KnowledgeSourceEnum statusEnum : KnowledgeSourceEnum.values()) {
            if (code.equals(statusEnum.getCode())) {
                return statusEnum;
            }
        }
        return IPUBLISH;
    }
}
