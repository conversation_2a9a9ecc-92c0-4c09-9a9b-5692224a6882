package com.unipus.digitalbook.model.enums;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025年06月03日 14:08
 */
public enum KnowledgeStatusEnum {

    UNPUBLISHED(0, "未发布"),
    PUBLISHED(1, "已发布"),
    OFFLINE(2, "下架"),

    ;
    private final Integer code;
    private final String desc;

    private static final Map<Integer, KnowledgeStatusEnum> CODE_MAP = new HashMap<>();

    static {
        for (KnowledgeStatusEnum type : KnowledgeStatusEnum.values()) {
            CODE_MAP.put(type.getCode(), type);
        }
    }

    KnowledgeStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static KnowledgeStatusEnum getByCode(Integer code) {
        return CODE_MAP.get(code);
    }

    public static List<KnowledgeStatusEnum> getAllKnowledgeStatus() {
        return List.of(KnowledgeStatusEnum.values());
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
