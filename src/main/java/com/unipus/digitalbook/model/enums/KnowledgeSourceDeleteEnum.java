package com.unipus.digitalbook.model.enums;

public enum KnowledgeSourceDeleteEnum {
    DELETE(1, "删除"),
    NO_DELETE(0, "未删除"),
    ;
    private final Integer code;
    private final String description;

    KnowledgeSourceDeleteEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static KnowledgeSourceDeleteEnum getStatus(Integer code) {
        for (KnowledgeSourceDeleteEnum statusEnum : KnowledgeSourceDeleteEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
