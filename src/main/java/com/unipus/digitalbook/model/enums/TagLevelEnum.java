package com.unipus.digitalbook.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TagLevelEnum {
    FIRST(0, "一级技能点"),
    SECOND(1, "二级技能点");
    private final Integer code;
    private final String description;

    public Boolean match(Integer code){
        return this.code.equals(code);
    }

    public Boolean match(TagLevelEnum level){
        return level != null && this.code.equals(level.getCode());
    }
}
