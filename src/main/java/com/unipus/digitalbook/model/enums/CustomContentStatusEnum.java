package com.unipus.digitalbook.model.enums;

import lombok.Getter;

/**
 * 自建内容状态枚举
 */
@Getter
public enum CustomContentStatusEnum {

    /**
     * 编写中
     */
    EDITING(0, "编写中"),

    /**
     * 待发布
     */
    PENDING(1, "待发布"),

    /**
     * 已发布
     */
    PUBLISHED(2, "已发布");

    private final Integer code;
    private final String desc;

    CustomContentStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 状态代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static CustomContentStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CustomContentStatusEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }

    /**
     * 判断给定的code是否匹配当前枚举
     *
     * @param code 状态代码
     * @return 是否匹配
     */
    public boolean match(Integer code) {
        return this.getCode().equals(code);
    }

    /**
     * 判断给定的枚举是否匹配当前枚举
     *
     * @param status 状态枚举
     * @return 是否匹配
     */
    public boolean match(CustomContentStatusEnum status) {
        return status != null && this.getCode().equals(status.getCode());
    }

    /**
     * 判断是否为编写中状态
     *
     * @param code 状态代码
     * @return 是否为编写中状态
     */
    public static boolean isEditing(Integer code) {
        return EDITING.getCode().equals(code);
    }

    /**
     * 判断是否为待发布状态
     *
     * @param code 状态代码
     * @return 是否为待发布状态
     */
    public static boolean isPending(Integer code) {
        return PENDING.getCode().equals(code);
    }

    /**
     * 判断是否为已发布状态
     *
     * @param code 状态代码
     * @return 是否为已发布状态
     */
    public static boolean isPublished(Integer code) {
        return PUBLISHED.getCode().equals(code);
    }
}
