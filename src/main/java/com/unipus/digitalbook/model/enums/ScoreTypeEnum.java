package com.unipus.digitalbook.model.enums;

import lombok.Getter;

/**
 * 分数类型枚举类
 */
@Getter
public enum ScoreTypeEnum {
    ORIGINAL(1, "原始分"),
    PERCENTAGE(2, "百分制");

    private final Integer code;
    private final String name;

    ScoreTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Boolean match(Integer code) {
        return this.getCode().equals(code);
    }

    public Boolean match(ScoreTypeEnum type) {
        return type != null && this.getCode().equals(type.getCode());
    }
}
