package com.unipus.digitalbook.model.enums;

import lombok.Getter;

/**
 * 自建内容类型枚举
 */
@Getter
public enum CustomContentTypeEnum {

    /**
     * 自定义章节
     */
    CUSTOM_CHAPTER(1, "自定义章节"),

    /**
     * 自定义段落
     */
    CUSTOM_PARAGRAPH(2, "自定义段落");

    private final Integer code;
    private final String desc;

    CustomContentTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 类型代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static CustomContentTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CustomContentTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 判断给定的code是否匹配当前枚举
     *
     * @param code 类型代码
     * @return 是否匹配
     */
    public boolean match(Integer code) {
        return this.getCode().equals(code);
    }

    /**
     * 判断给定的枚举是否匹配当前枚举
     *
     * @param type 类型枚举
     * @return 是否匹配
     */
    public boolean match(CustomContentTypeEnum type) {
        return type != null && this.getCode().equals(type.getCode());
    }
}
