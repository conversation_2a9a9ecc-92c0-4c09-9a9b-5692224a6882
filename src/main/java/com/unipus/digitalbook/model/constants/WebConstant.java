package com.unipus.digitalbook.model.constants;

/**
 * <AUTHOR>
 * @date 2024/10/16 下午3:24
 */
public class WebConstant {
    public static final String HEADER_AUTHORIZATION = "Authorization";
    public static final String HEADER_TOKEN_TYPE = "Bearer";
    public static final String HEADER_ORG_ID = "orgId";
    public static final String CONTENT_TYPE_JSON_UTF8 = "application/json; charset=UTF-8";

    public static final String SERVICE_TICKET_IN_HEADER="serviceTicket";
    //起名晦涩难懂一些 为了安全
    public static final String JWT_SERVICE_TICKET="jst";
    public static final String JWT_USER_ID="juid";
    public static final String JWT_USER_NAME="jun";

    public static final String JWT_BOOK_ID="bid";

    public static final String JWT_CHAPTER_ID="cid";

    // 用于内部用户访问认证：访问随机ID
    public static final String JWT_BACKEND_SID ="bsid";
    // 用于内部用户访问认证：openID
    public static final String JWT_BACKEND_OID ="boid";
    // 用于内部用户访问认证：读者类型
    public static final String JWT_BACKEND_RID ="brid";
    // 用于内部用户访问认证：APPID
    public static final String JWT_BACKEND_AID ="baid";
    // 用于内部用户访问认证：用户数据包
    public static final String JWT_BACKEND_PID ="bpid";

    // 用于内部用户访问认证：环境分区ID
    public static final String JWT_ENV_PID ="epid";
    // 后端用户默认的appId
    public static final Long BACKEND_USER_APPID = 1L;
}
