package com.unipus.digitalbook.model.po.complement;

import com.unipus.digitalbook.model.entity.complement.ComplementResourceReference;

import java.io.Serializable;
import java.util.Date;

/**
 * 教材配套资源引用
 * @TableName complement_resource_reference
 */
public class ComplementResourceReferencePO implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 配套资源ID
     */
    private String complementResourceId;

    /**
     * 媒体位置
     */
    private String position;

    /**
     * 章节id
     */
    private String chapterId;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否生效：true生效，false禁用
     */
    private Boolean enable;

    // Getter and Setter methods

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getComplementResourceId() {
        return complementResourceId;
    }

    public void setComplementResourceId(String complementResourceId) {
        this.complementResourceId = complementResourceId;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public static ComplementResourceReferencePO build(ComplementResourceReference entity, Long userId){
        ComplementResourceReferencePO complementResourceReferencePO = new ComplementResourceReferencePO();
        complementResourceReferencePO.setId(entity.getId());
        complementResourceReferencePO.setComplementResourceId(entity.getComplementResourceId());
        complementResourceReferencePO.setPosition(entity.getPosition());
        complementResourceReferencePO.setChapterId(entity.getChapterId());
        complementResourceReferencePO.setEnable(true);
        complementResourceReferencePO.setCreateBy(userId);
        complementResourceReferencePO.setUpdateBy(userId);
        return complementResourceReferencePO;
    }

    public ComplementResourceReference toEntity() {
        ComplementResourceReference entity = new ComplementResourceReference();
        entity.setId(this.id);
        entity.setComplementResourceId(this.complementResourceId);
        entity.setPosition(this.position);
        entity.setChapterId(this.chapterId);
        entity.setEnable(this.enable);
        return entity;
    }

}
