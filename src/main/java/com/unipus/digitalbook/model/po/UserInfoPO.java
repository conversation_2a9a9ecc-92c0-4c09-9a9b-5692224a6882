package com.unipus.digitalbook.model.po;

import com.unipus.digitalbook.model.entity.UserInfo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.StringJoiner;

/**
 * 表：user_info
 *
 * <AUTHOR>
 * @date 2024年11月27日 11:13:07
 */
public class UserInfoPO implements Serializable {
    /**
     * 数据库字段： user_info.id
     */
    private Long id;

    /**
     * sso ID
     * <p>
     * 数据库字段： user_info.sso_id
     */
    private String ssoId;

    /**
     * 姓名
     * <p>
     * 数据库字段： user_info.name
     */
    private String name;

    /**
     * 手机号
     * <p>
     * 数据库字段： user_info.cell_phone
     */
    private String cellPhone;

    /**
     * 邮箱地址
     * <p>
     * 数据库字段： user_info.email
     */
    private String email;

    /**
     * 性别
     * 0:女 1:男 2:未知
     * <p>
     * 数据库字段： user_info.gender
     */
    private Integer gender;

    /**
     * 简介
     * <p>
     * 数据库字段： user_info.desc
     */
    private String desc;

    /**
     * 头像地址
     * <p>
     * 数据库字段： user_info.avatar_url
     */
    private String avatarUrl;

    /**
     * 状态
     * true:生效 false:无效
     * <p>
     * 数据库字段： user_info.enable
     */
    private Boolean enable;

    /**
     * 创建时间
     * <p>
     * 数据库字段： user_info.create_time
     */
    private Date createTime;

    /**
     * 最后更新时间
     * <p>
     * 数据库字段： user_info.update_time
     */
    private Date updateTime;

    /**
     * 创建者ID
     * <p>
     * 数据库字段： user_info.create_by
     */
    private Long createBy;

    /**
     * 最后更新者ID
     * <p>
     * 数据库字段： user_info.update_by
     */
    private Long updateBy;

    /**
     * 激活时间
     * <p>
     * 数据库字段： user_info.active_time
     */
    private Date activeTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table user_info
     */
    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 状态
     * 0:未激活 1:已激活
     */
    private Integer status;


    /**
     * 获取user_info.id
     *
     * @return user_info 的值.id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置user_info.id
     *
     * @param id the value for user_info.id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取user_info.sso_id
     *
     * @return user_info 的值.sso_id
     */
    public String getSsoId() {
        return ssoId;
    }

    /**
     * 设置user_info.sso_id
     *
     * @param ssoId the value for user_info.sso_id
     */
    public void setSsoId(String ssoId) {
        this.ssoId = ssoId == null ? null : ssoId.trim();
    }

    /**
     * 获取user_info.name
     *
     * @return user_info 的值.name
     */
    public String getName() {
        return name;
    }

    /**
     * 设置user_info.name
     *
     * @param name the value for user_info.name
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * 获取user_info.cell_phone
     *
     * @return user_info 的值.cell_phone
     */
    public String getCellPhone() {
        return cellPhone;
    }

    /**
     * 设置user_info.cell_phone
     *
     * @param cellPhone the value for user_info.cell_phone
     */
    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone == null ? null : cellPhone.trim();
    }

    /**
     * 获取user_info.email
     *
     * @return user_info 的值.email
     */
    public String getEmail() {
        return email;
    }

    /**
     * 设置user_info.email
     *
     * @param email the value for user_info.email
     */
    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    /**
     * 获取user_info.gender
     *
     * @return user_info 的值.gender
     */
    public Integer getGender() {
        return gender;
    }

    /**
     * 设置user_info.gender
     *
     * @param gender the value for user_info.gender
     */
    public void setGender(Integer gender) {
        this.gender = gender;
    }

    /**
     * 获取user_info.desc
     *
     * @return user_info 的值.desc
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 设置user_info.desc
     *
     * @param desc the value for user_info.desc
     */
    public void setDesc(String desc) {
        this.desc = desc == null ? null : desc.trim();
    }

    /**
     * 获取user_info.avatar_url
     *
     * @return user_info 的值.avatar_url
     */
    public String getAvatarUrl() {
        return avatarUrl;
    }

    /**
     * 设置user_info.avatar_url
     *
     * @param avatarUrl the value for user_info.avatar_url
     */
    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl == null ? null : avatarUrl.trim();
    }

    /**
     * 获取user_info.enable
     *
     * @return user_info 的值.enable
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 设置user_info.enable
     *
     * @param enable the value for user_info.enable
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    /**
     * 获取user_info.create_time
     *
     * @return user_info 的值.create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置user_info.create_time
     *
     * @param createTime the value for user_info.create_time
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取user_info.update_time
     *
     * @return user_info 的值.update_time
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置user_info.update_time
     *
     * @param updateTime the value for user_info.update_time
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取user_info.create_by
     *
     * @return user_info 的值.create_by
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * 设置user_info.create_by
     *
     * @param createBy the value for user_info.create_by
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * 获取user_info.update_by
     *
     * @return user_info 的值.update_by
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * 设置user_info.update_by
     *
     * @param updateBy the value for user_info.update_by
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 获取user_info.active_time
     *
     * @return user_info 的值.active_time
     */
    public Date getActiveTime() {
        return activeTime;
    }

    /**
     * 设置user_info.active_time
     *
     * @param activeTime the value for user_info.active_time
     */
    public void setActiveTime(Date activeTime) {
        this.activeTime = activeTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 转字符
     *
     * @return String
     */
    @Override
    public String toString() {
        return new StringJoiner(", ", UserInfoPO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("ssoId='" + ssoId + "'")
                .add("name='" + name + "'")
                .add("cellPhone='" + cellPhone + "'")
                .add("email='" + email + "'")
                .add("gender=" + gender)
                .add("desc='" + desc + "'")
                .add("avatarUrl='" + avatarUrl + "'")
                .add("enable=" + enable)
                .add("createTime=" + createTime)
                .add("updateTime=" + updateTime)
                .add("createBy=" + createBy)
                .add("updateBy=" + updateBy)
                .add("activeTime=" + activeTime)
                .add("status=" + status)
                .toString();
    }

    /**
     * 将UserInfoPO对象转换为UserInfo对象
     *
     * @return UserInfo 对象
     */
    public UserInfo toEntity() {
        // 创建一个新的UserInfo对象
        UserInfo userInfo = new UserInfo();
        // 将UserInfoPO中的各个属性值赋给UserInfo对象
        userInfo.setId(this.getId());
        userInfo.setSsoId(this.getSsoId());
        userInfo.setName(this.getName());
        userInfo.setCellPhone(this.getCellPhone());
        userInfo.setEmail(this.getEmail());
        userInfo.setGender(this.getGender());
        userInfo.setDesc(this.getDesc());
        userInfo.setAvatarUrl(this.getAvatarUrl());
        userInfo.setEnable(this.getEnable());
        userInfo.setCreateTime(this.getCreateTime());
        userInfo.setUpdateTime(this.getUpdateTime());
        userInfo.setCreateBy(this.getCreateBy());
        userInfo.setUpdateBy(this.getUpdateBy());
        userInfo.setActiveTime(this.getActiveTime());

        // 返回填充好的UserInfo对象
        return userInfo;
    }
    public UserInfoPO createNew(String name, String cellPhone) {
        UserInfoPO po = new UserInfoPO();
        po.setName(name);
        po.setCellPhone(cellPhone);
        po.setEnable(true);
        return po;
    }

}