package com.unipus.digitalbook.model.po.question;

import com.unipus.digitalbook.model.entity.question.ChoiceQuestionOption;

import java.io.Serializable;
import java.util.Date;
/**
 * @TableName choice_question_option
 */
public class ChoiceQuestionOptionPO implements Serializable {
    /**
     * ID
     */
    private Long id;

    /**
     * 题目ID
     */
    private Long questionId;

    /**
     * 选项id
     */
    private String optionId;

    /**
     * 选项的名字 A B C D
     */
    private String name;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 选项内容
     */
    private String content;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;
    /**
     * ID
     */
    public Long getId() {
        return id;
    }

    /**
     * ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 题目ID
     */
    public Long getQuestionId() {
        return questionId;
    }

    /**
     * 题目ID
     */
    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    /**
     * 选项id
     */
    public String getOptionId() {
        return optionId;
    }

    /**
     * 选项id
     */
    public void setOptionId(String optionId) {
        this.optionId = optionId;
    }

    /**
     * 选项的名字 A B C D
     */
    public String getName() {
        return name;
    }

    /**
     * 选项的名字 A B C D
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 选项内容
     */
    public String getContent() {
        return content;
    }

    /**
     * 选项内容
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 最后更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 最后更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 创建者ID
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * 创建者ID
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * 最后更新者ID
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * 最后更新者ID
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 是否有效 0-无效 1-有效
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 是否有效 0-无效 1-有效
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public ChoiceQuestionOptionPO() {
    }

    /**
     * 将ChoiceQuestionOption实体转换为PO
     *
     * @param option ChoiceQuestionOption实体
     */
    public ChoiceQuestionOptionPO(ChoiceQuestionOption option, Long questionId) {
        this.setOptionId(option.getOptionId());
        this.setQuestionId(questionId);
        this.setName(option.getName());
        this.setSortOrder(option.getSortOrder());
        this.setContent(option.getContent());
        this.setCreateBy(option.getCreateBy());
        this.setUpdateBy(option.getUpdateBy());
    }

    public ChoiceQuestionOption toEntity() {
        ChoiceQuestionOption option = new ChoiceQuestionOption();
        option.setId(this.getId());
        option.setOptionId(this.getOptionId());
        option.setName(this.getName());
        option.setEnable(this.getEnable());
        option.setQuestionId(this.getQuestionId());
        option.setContent(this.getContent());
        option.setCreateBy(this.getCreateBy());
        option.setUpdateBy(this.getUpdateBy());
        option.setSortOrder(this.getSortOrder());
        return option;
    }

}