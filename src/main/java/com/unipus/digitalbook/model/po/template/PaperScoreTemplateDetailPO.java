package com.unipus.digitalbook.model.po.template;

import com.unipus.digitalbook.model.entity.template.PaperScoreTemplateDetail;
import lombok.Data;

import java.util.Date;

@Data
public class PaperScoreTemplateDetailPO {
    private Long id;

    private Long paperScoreTemplateId;

    private Integer minScore;

    private Integer maxScore;

    private Integer evaluatePhrases;

    private String evaluateText;

    private Date createTime;

    private Date updateTime;

    private Long createBy;

    private Long updateBy;

    private Boolean enable;

    public void fromEntity(PaperScoreTemplateDetail paperScoreTemplateDetail) {
        this.setMinScore(paperScoreTemplateDetail.getMinScore());
        this.setMaxScore(paperScoreTemplateDetail.getMaxScore());
        this.setEvaluatePhrases(paperScoreTemplateDetail.getEvaluatePhrases());
        this.setEvaluateText(paperScoreTemplateDetail.getEvaluateText());
    }

    public PaperScoreTemplateDetail toEntity(){
        PaperScoreTemplateDetail paperScoreTemplateDetail = new PaperScoreTemplateDetail();
        paperScoreTemplateDetail.setId(this.getId());
        paperScoreTemplateDetail.setMinScore(this.getMinScore());
        paperScoreTemplateDetail.setMaxScore(this.getMaxScore());
        paperScoreTemplateDetail.setEvaluatePhrases(this.getEvaluatePhrases());
        paperScoreTemplateDetail.setEvaluateText(this.getEvaluateText());
        return paperScoreTemplateDetail;
    }

    public PaperScoreTemplateDetailPO clone(Long createBy, Long paperScoreTemplateId){
        PaperScoreTemplateDetailPO paperScoreTemplateDetailPO = new PaperScoreTemplateDetailPO();
        paperScoreTemplateDetailPO.setPaperScoreTemplateId(paperScoreTemplateId);
        paperScoreTemplateDetailPO.setMinScore(this.getMinScore());
        paperScoreTemplateDetailPO.setMaxScore(this.getMaxScore());
        paperScoreTemplateDetailPO.setEvaluatePhrases(this.getEvaluatePhrases());
        paperScoreTemplateDetailPO.setEvaluateText(this.getEvaluateText());
        paperScoreTemplateDetailPO.setCreateBy(createBy);
        paperScoreTemplateDetailPO.setUpdateBy(createBy);
        return paperScoreTemplateDetailPO;
    }
}