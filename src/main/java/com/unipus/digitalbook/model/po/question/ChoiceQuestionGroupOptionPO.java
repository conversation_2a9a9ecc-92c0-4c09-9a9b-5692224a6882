package com.unipus.digitalbook.model.po.question;

import com.unipus.digitalbook.model.entity.question.ChoiceQuestionOption;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName choice_question_group_option
 */
public class ChoiceQuestionGroupOptionPO implements Serializable {
    /**
     * ID
     */
    private Long id;

    /**
     * 题组ID
     */
    private Long groupId;

    /**
     * 选项id
     */
    private String optionId;

    /**
     * 选项的名字 A B C D
     */
    private String name;

    /**
     * 选项内容
     */
    private String content;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

    /**
     * ID
     */
    public Long getId() {
        return id;
    }

    /**
     * ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 题组ID
     */
    public Long getGroupId() {
        return groupId;
    }

    /**
     * 题组ID
     */
    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    /**
     * 选项id
     */
    public String getOptionId() {
        return optionId;
    }

    /**
     * 选项id
     */
    public void setOptionId(String optionId) {
        this.optionId = optionId;
    }

    /**
     * 选项的名字 A B C D
     */
    public String getName() {
        return name;
    }

    /**
     * 选项的名字 A B C D
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 选项内容
     */
    public String getContent() {
        return content;
    }

    /**
     * 选项内容
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 最后更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 最后更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 创建者ID
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * 创建者ID
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * 最后更新者ID
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * 最后更新者ID
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 是否有效 0-无效 1-有效
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 是否有效 0-无效 1-有效
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public ChoiceQuestionGroupOptionPO() {
    }

    /**
     * 将ChoiceQuestionOption实体转换为PO
     *
     * @param option ChoiceQuestionOption实体
     */
    public ChoiceQuestionGroupOptionPO(ChoiceQuestionOption option, Long groupId) {
        this.setOptionId(option.getOptionId());
        this.setGroupId(groupId);
        this.setName(option.getName());
        this.setContent(option.getContent());
        this.setCreateBy(option.getCreateBy());
        this.setUpdateBy(option.getUpdateBy());
    }

    public ChoiceQuestionOption toEntity() {
        ChoiceQuestionOption option = new ChoiceQuestionOption();
        option.setOptionId(this.getOptionId());
        option.setName(this.getName());
        option.setContent(this.getContent());
        option.setCreateBy(this.getCreateBy());
        option.setUpdateBy(this.getUpdateBy());
        return option;
    }
}