package com.unipus.digitalbook.model.po.chapter;

import com.unipus.digitalbook.model.entity.chapter.ChapterTemplate;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.StringJoiner;

/**
 * 表：chapter_template
 *
 * <AUTHOR>
 * @date 2025年05月26日 10:24:04
 */
public class ChapterTemplatePO implements Serializable {
    /**
     * 数据库字段： chapter_template.id
     */
    private Integer id;

    /**
     * 模版名称
     * <p>
     * 数据库字段： chapter_template.name
     */
    private String name;

    /**
     * 模版来源
     * <p>
     * 数据库字段： chapter_template.from_chapter_id
     */
    private String fromChapterId;

    /**
     * 模版json的存储地址
     * <p>
     * 数据库字段： chapter_template.url
     */
    private String url;

    /**
     * 模版用于预览的图片地址
     * <p>
     * 数据库字段： chapter_template.preview_image_url
     */
    private String previewImageUrl;

    /**
     * 创建时间
     * <p>
     * 数据库字段： chapter_template.create_time
     */
    private Date createTime;

    /**
     * 最后更新时间
     * <p>
     * 数据库字段： chapter_template.update_time
     */
    private Date updateTime;

    /**
     * 创建者ID
     * <p>
     * 数据库字段： chapter_template.create_by
     */
    private Long createBy;

    /**
     * 最后更新者ID
     * <p>
     * 数据库字段： chapter_template.update_by
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     * <p>
     * 数据库字段： chapter_template.enable
     */
    private Boolean enable;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table chapter_template
     */
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取chapter_template.id
     *
     * @return chapter_template 的值.id
     */
    public Integer getId() {
        return id;
    }

    /**
     * 设置chapter_template.id
     *
     * @param id the value for chapter_template.id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取chapter_template.name
     *
     * @return chapter_template 的值.name
     */
    public String getName() {
        return name;
    }

    /**
     * 设置chapter_template.name
     *
     * @param name the value for chapter_template.name
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * 获取chapter_template.from_chapter_id
     *
     * @return chapter_template 的值.from_chapter_id
     */
    public String getFromChapterId() {
        return fromChapterId;
    }

    /**
     * 设置chapter_template.from_chapter_id
     *
     * @param fromChapterId the value for chapter_template.from_chapter_id
     */
    public void setFromChapterId(String fromChapterId) {
        this.fromChapterId = fromChapterId == null ? null : fromChapterId.trim();
    }

    /**
     * 获取chapter_template.url
     *
     * @return chapter_template 的值.url
     */
    public String getUrl() {
        return url;
    }

    /**
     * 设置chapter_template.url
     *
     * @param url the value for chapter_template.url
     */
    public void setUrl(String url) {
        this.url = url == null ? null : url.trim();
    }

    /**
     * 获取chapter_template.preview_image_url
     *
     * @return chapter_template 的值.preview_image_url
     */
    public String getPreviewImageUrl() {
        return previewImageUrl;
    }

    /**
     * 设置chapter_template.preview_image_url
     *
     * @param previewImageUrl the value for chapter_template.preview_image_url
     */
    public void setPreviewImageUrl(String previewImageUrl) {
        this.previewImageUrl = previewImageUrl == null ? null : previewImageUrl.trim();
    }

    /**
     * 获取chapter_template.create_time
     *
     * @return chapter_template 的值.create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置chapter_template.create_time
     *
     * @param createTime the value for chapter_template.create_time
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取chapter_template.update_time
     *
     * @return chapter_template 的值.update_time
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置chapter_template.update_time
     *
     * @param updateTime the value for chapter_template.update_time
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取chapter_template.create_by
     *
     * @return chapter_template 的值.create_by
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * 设置chapter_template.create_by
     *
     * @param createBy the value for chapter_template.create_by
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * 获取chapter_template.update_by
     *
     * @return chapter_template 的值.update_by
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * 设置chapter_template.update_by
     *
     * @param updateBy the value for chapter_template.update_by
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 获取chapter_template.enable
     *
     * @return chapter_template 的值.enable
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 设置chapter_template.enable
     *
     * @param enable the value for chapter_template.enable
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }



    public void fromEntity(ChapterTemplate template) {
        if (template != null) {
            this.setId(template.getId());
            this.setName(template.getName());
            this.setFromChapterId(template.getFromChapterId());
            this.setUrl(template.getUrl());
            this.setPreviewImageUrl(template.getPreviewImageUrl());
            this.setCreateTime(template.getCreateTime());
            this.setUpdateTime(template.getUpdateTime());
            this.setCreateBy(template.getCreateBy());
            this.setUpdateBy(template.getUpdateBy());
            this.setEnable(template.getEnable());
        }
    }

    public ChapterTemplate toEntity() {
        ChapterTemplate template = new ChapterTemplate();
        template.setId(this.getId());
        template.setName(this.getName());
        template.setFromChapterId(this.getFromChapterId());
        template.setUrl(this.getUrl());
        template.setPreviewImageUrl(this.getPreviewImageUrl());
        template.setCreateTime(this.getCreateTime());
        template.setUpdateTime(this.getUpdateTime());
        template.setCreateBy(this.getCreateBy());
        template.setUpdateBy(this.getUpdateBy());
        template.setEnable(this.getEnable());
        return template;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ChapterTemplatePO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("name='" + name + "'")
                .add("fromChapterId='" + fromChapterId + "'")
                .add("url='" + url + "'")
                .add("previewImageUrl='" + previewImageUrl + "'")
                .add("createTime=" + createTime)
                .add("updateTime=" + updateTime)
                .add("createBy=" + createBy)
                .add("updateBy=" + updateBy)
                .add("enable=" + enable)
                .toString();
    }
}