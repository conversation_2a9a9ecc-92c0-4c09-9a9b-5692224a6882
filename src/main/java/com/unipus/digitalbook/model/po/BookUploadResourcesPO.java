package com.unipus.digitalbook.model.po;

import com.unipus.digitalbook.model.entity.book.BookFile;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.StringJoiner;

/**
 * 书籍静态资源信息表
 * 表：book_upload_resources
 *
 * <AUTHOR>
 * @date 2025年05月28日 17:34:06
 */
public class BookUploadResourcesPO implements Serializable {
    /**
     * 资源唯一ID (主键)
     * <p>
     * 数据库字段： book_upload_resources.id
     */
    private Long id;

    /**
     * 关联书籍的ID
     * <p>
     * 数据库字段： book_upload_resources.book_id
     */
    private String bookId;

    /**
     * 资源名称（例如：原始文件名或用户自定义名称）
     * <p>
     * 数据库字段： book_upload_resources.resource_name
     */
    private String resourceName;

    /**
     * 资源的存储路径或URL
     * <p>
     * 数据库字段： book_upload_resources.storage_path
     */
    private String storagePath;

    /**
     * 资源类型 (例如：IMAGE, AUDIO, VIDEO, DOCUMENT)
     * <p>
     * 数据库字段： book_upload_resources.resource_type
     */
    private String resourceType;

    /**
     * MIME类型 (例如：image/jpeg, audio/mpeg, application/pdf)
     * <p>
     * 数据库字段： book_upload_resources.mime_type
     */
    private String mimeType;

    /**
     * 文件大小（字节）
     * <p>
     * 数据库字段： book_upload_resources.file_size_bytes
     */
    private Long fileSizeBytes;

    /**
     * 上传者用户ID (如果需要追踪上传者，可以关联用户表)
     * <p>
     * 数据库字段： book_upload_resources.uploader_id
     */
    private Long uploaderId;

    /**
     * 资源状态 (例如：ACTIVE, DELETED, PENDING)
     * <p>
     * 数据库字段： book_upload_resources.status
     */
    private String status;

    /**
     * 创建时间
     * <p>
     * 数据库字段： book_upload_resources.create_time
     */
    private Date createTime;

    /**
     * 最后更新时间
     * <p>
     * 数据库字段： book_upload_resources.update_time
     */
    private Date updateTime;

    /**
     * 创建者ID
     * <p>
     * 数据库字段： book_upload_resources.create_by
     */
    private Long createBy;

    /**
     * 最后更新者ID
     * <p>
     * 数据库字段： book_upload_resources.update_by
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     * <p>
     * 数据库字段： book_upload_resources.enable
     */
    private Boolean enable;

    /**
     * 资源描述或备注 (可选)
     * <p>
     * 数据库字段： book_upload_resources.description
     */
    private String description;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table book_upload_resources
     */
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取book_upload_resources.id
     *
     * @return book_upload_resources 的值.id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置book_upload_resources.id
     *
     * @param id the value for book_upload_resources.id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取book_upload_resources.book_id
     *
     * @return book_upload_resources 的值.book_id
     */
    public String getBookId() {
        return bookId;
    }

    /**
     * 设置book_upload_resources.book_id
     *
     * @param bookId the value for book_upload_resources.book_id
     */
    public void setBookId(String bookId) {
        this.bookId = bookId == null ? null : bookId.trim();
    }

    /**
     * 获取book_upload_resources.resource_name
     *
     * @return book_upload_resources 的值.resource_name
     */
    public String getResourceName() {
        return resourceName;
    }

    /**
     * 设置book_upload_resources.resource_name
     *
     * @param resourceName the value for book_upload_resources.resource_name
     */
    public void setResourceName(String resourceName) {
        this.resourceName = resourceName == null ? null : resourceName.trim();
    }

    /**
     * 获取book_upload_resources.storage_path
     *
     * @return book_upload_resources 的值.storage_path
     */
    public String getStoragePath() {
        return storagePath;
    }

    /**
     * 设置book_upload_resources.storage_path
     *
     * @param storagePath the value for book_upload_resources.storage_path
     */
    public void setStoragePath(String storagePath) {
        this.storagePath = storagePath == null ? null : storagePath.trim();
    }

    /**
     * 获取book_upload_resources.resource_type
     *
     * @return book_upload_resources 的值.resource_type
     */
    public String getResourceType() {
        return resourceType;
    }

    /**
     * 设置book_upload_resources.resource_type
     *
     * @param resourceType the value for book_upload_resources.resource_type
     */
    public void setResourceType(String resourceType) {
        this.resourceType = resourceType == null ? null : resourceType.trim();
    }

    /**
     * 获取book_upload_resources.mime_type
     *
     * @return book_upload_resources 的值.mime_type
     */
    public String getMimeType() {
        return mimeType;
    }

    /**
     * 设置book_upload_resources.mime_type
     *
     * @param mimeType the value for book_upload_resources.mime_type
     */
    public void setMimeType(String mimeType) {
        this.mimeType = mimeType == null ? null : mimeType.trim();
    }

    /**
     * 获取book_upload_resources.file_size_bytes
     *
     * @return book_upload_resources 的值.file_size_bytes
     */
    public Long getFileSizeBytes() {
        return fileSizeBytes;
    }

    /**
     * 设置book_upload_resources.file_size_bytes
     *
     * @param fileSizeBytes the value for book_upload_resources.file_size_bytes
     */
    public void setFileSizeBytes(Long fileSizeBytes) {
        this.fileSizeBytes = fileSizeBytes;
    }

    /**
     * 获取book_upload_resources.uploader_id
     *
     * @return book_upload_resources 的值.uploader_id
     */
    public Long getUploaderId() {
        return uploaderId;
    }

    /**
     * 设置book_upload_resources.uploader_id
     *
     * @param uploaderId the value for book_upload_resources.uploader_id
     */
    public void setUploaderId(Long uploaderId) {
        this.uploaderId = uploaderId;
    }

    /**
     * 获取book_upload_resources.status
     *
     * @return book_upload_resources 的值.status
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置book_upload_resources.status
     *
     * @param status the value for book_upload_resources.status
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * 获取book_upload_resources.create_time
     *
     * @return book_upload_resources 的值.create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置book_upload_resources.create_time
     *
     * @param createTime the value for book_upload_resources.create_time
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取book_upload_resources.update_time
     *
     * @return book_upload_resources 的值.update_time
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置book_upload_resources.update_time
     *
     * @param updateTime the value for book_upload_resources.update_time
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取book_upload_resources.create_by
     *
     * @return book_upload_resources 的值.create_by
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * 设置book_upload_resources.create_by
     *
     * @param createBy the value for book_upload_resources.create_by
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * 获取book_upload_resources.update_by
     *
     * @return book_upload_resources 的值.update_by
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * 设置book_upload_resources.update_by
     *
     * @param updateBy the value for book_upload_resources.update_by
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 获取book_upload_resources.enable
     *
     * @return book_upload_resources 的值.enable
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 设置book_upload_resources.enable
     *
     * @param enable the value for book_upload_resources.enable
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    /**
     * 获取book_upload_resources.description
     *
     * @return book_upload_resources 的值.description
     */
    public String getDescription() {
        return description;
    }

    /**
     * 设置book_upload_resources.description
     *
     * @param description the value for book_upload_resources.description
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * 转字符
     *
     * @return String
     */
    @Override
    public String toString() {
        return new StringJoiner(", ", BookUploadResourcesPO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("bookId='" + bookId + "'")
                .add("resourceName='" + resourceName + "'")
                .add("storagePath='" + storagePath + "'")
                .add("resourceType='" + resourceType + "'")
                .add("mimeType='" + mimeType + "'")
                .add("fileSizeBytes=" + fileSizeBytes)
                .add("uploaderId=" + uploaderId)
                .add("status='" + status + "'")
                .add("createTime=" + createTime)
                .add("updateTime=" + updateTime)
                .add("createBy=" + createBy)
                .add("updateBy=" + updateBy)
                .add("enable=" + enable)
                .add("description='" + description + "'")
                .toString();
    }

    public BookFile toEntity() {
        BookFile bookFile = new BookFile();
        bookFile.setId(this.getId());
        bookFile.setBookId(this.getBookId());
        bookFile.setResourceName(this.getResourceName());
        bookFile.setStoragePath(this.getStoragePath());
        bookFile.setResourceType(this.getResourceType());
        bookFile.setMimeType(this.getMimeType());
        bookFile.setFileSizeBytes(this.getFileSizeBytes());
        bookFile.setUploaderId(this.getUploaderId());
        bookFile.setStatus(this.getStatus());
        bookFile.setCreateTime(this.getCreateTime());
        bookFile.setUpdateTime(this.getUpdateTime());
        bookFile.setCreateBy(this.getCreateBy());
        bookFile.setUpdateBy(this.getUpdateBy());
        bookFile.setEnable(this.getEnable());
        bookFile.setDescription(this.getDescription());
        return bookFile;
    }

    public BookUploadResourcesPO() {
        super();
    }

    public BookUploadResourcesPO(BookFile bookFile) {
        if (bookFile != null) {
            this.id = bookFile.getId();
            this.bookId = bookFile.getBookId();
            this.resourceName = bookFile.getResourceName();
            this.storagePath = bookFile.getStoragePath();
            this.resourceType = bookFile.getResourceType();
            this.mimeType = bookFile.getMimeType();
            this.fileSizeBytes = bookFile.getFileSizeBytes();
            this.uploaderId = bookFile.getUploaderId();
            this.status = bookFile.getStatus();
            this.createTime = bookFile.getCreateTime();
            this.updateTime = bookFile.getUpdateTime();
            this.createBy = bookFile.getCreateBy();
            this.updateBy = bookFile.getUpdateBy();
            this.enable = bookFile.getEnable();
            this.description = bookFile.getDescription();
        } else {
            // 如果传入的bookFile为null，可以选择抛出异常或者初始化为空值
            throw new IllegalArgumentException("BookFile cannot be null");
        }
    }


}