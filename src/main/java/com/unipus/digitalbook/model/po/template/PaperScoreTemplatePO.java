package com.unipus.digitalbook.model.po.template;

import com.unipus.digitalbook.model.entity.template.PaperScoreTemplate;
import com.unipus.digitalbook.model.enums.PaperScoreTemplateATEnum;
import com.unipus.digitalbook.model.enums.PaperScoreTemplateStatusEnum;
import lombok.Data;

import java.util.Date;

@Data
public class PaperScoreTemplatePO {
    private Long id;

    private String name;

    private Integer type;

    private Integer status;

    private Date createTime;

    private Date updateTime;

    private Long createBy;

    private Long updateBy;

    private Boolean enable;

    private Integer attributeType;

    private Long parentId;

    private Integer version;

    public void fromEntity(PaperScoreTemplate paperScoreTemplate) {
        this.setName(paperScoreTemplate.getName());
        this.setType(paperScoreTemplate.getType());
        this.setStatus(paperScoreTemplate.getStatus());
    }

    public PaperScoreTemplate toEntity(){
        PaperScoreTemplate paperScoreTemplate = new PaperScoreTemplate();
        paperScoreTemplate.setId(this.getId());
        paperScoreTemplate.setName(this.getName());
        paperScoreTemplate.setType(this.getType());
        paperScoreTemplate.setStatus(this.getStatus());
        paperScoreTemplate.setCreateUserId(this.getCreateBy());
        paperScoreTemplate.setUpdateTime(this.getUpdateTime());
        return paperScoreTemplate;
    }

    public PaperScoreTemplatePO clone(Long createBy){
        PaperScoreTemplatePO paperScoreTemplatePO = new PaperScoreTemplatePO();
        paperScoreTemplatePO.setName(this.getName());
        paperScoreTemplatePO.setType(this.getType());
        paperScoreTemplatePO.setStatus(PaperScoreTemplateStatusEnum.PUBLISHED.getCode());
        paperScoreTemplatePO.setAttributeType(PaperScoreTemplateATEnum.CLONE.getCode());
        paperScoreTemplatePO.setParentId(this.getId());
        paperScoreTemplatePO.setVersion(this.getVersion());
        paperScoreTemplatePO.setCreateBy(createBy);
        paperScoreTemplatePO.setUpdateBy(createBy);
        return paperScoreTemplatePO;
    }
}