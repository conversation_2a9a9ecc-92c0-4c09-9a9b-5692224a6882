package com.unipus.digitalbook.model.po;

import com.unipus.digitalbook.model.entity.Organization;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.StringJoiner;

/**
 * 机构表
 * 表：organization
 *
 * <AUTHOR>
 * @date 2024年12月02日 01:52:39
 */
public class OrganizationPO implements Serializable {
    /**
     * 主键ID
     * <p>
     * 数据库字段： organization.id
     *
     */
    private Long id;

    /**
     * 机构名称
     * <p>
     * 数据库字段： organization.org_name
     *
     */
    private String orgName;

    /**
     * 机构类型：公司/集团
     * <p>
     * 数据库字段： organization.org_type
     *
     */
    private Integer orgType;

    /**
     * 上级机构ID
     * <p>
     * 数据库字段： organization.parent_id
     *
     */
    private Long parentId;

    /**
     * 状态：1启用/0禁用
     * <p>
     * 数据库字段： organization.status
     *
     */
    private Integer status;

    /**
     * 创建时间
     * <p>
     * 数据库字段： organization.create_time
     *
     */
    private Date createTime;

    /**
     * 最后更新时间
     * <p>
     * 数据库字段： organization.update_time
     *
     */
    private Date updateTime;

    /**
     * 创建者ID
     * <p>
     * 数据库字段： organization.create_by
     *
     */
    private Long createBy;

    /**
     * 最后更新者ID
     * <p>
     * 数据库字段： organization.update_by
     *
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     * <p>
     * 数据库字段： organization.enable
     *
     */
    private Boolean enable;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table organization
     *
     */
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取organization.id
     *
     * @return organization 的值.id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置organization.id
     *
     * @param id the value for organization.id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取organization.org_name
     *
     * @return organization 的值.org_name
     */
    public String getOrgName() {
        return orgName;
    }

    /**
     * 设置organization.org_name
     *
     * @param orgName the value for organization.org_name
     */
    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }

    /**
     * 获取organization.org_type
     *
     * @return organization 的值.org_type
     */
    public Integer getOrgType() {
        return orgType;
    }

    /**
     * 设置organization.org_type
     *
     * @param orgType the value for organization.org_type
     */
    public void setOrgType(Integer orgType) {
        this.orgType = orgType;
    }

    /**
     * 获取organization.parent_id
     *
     * @return organization 的值.parent_id
     */
    public Long getParentId() {
        return parentId;
    }

    /**
     * 设置organization.parent_id
     *
     * @param parentId the value for organization.parent_id
     */
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    /**
     * 获取organization.status
     *
     * @return organization 的值.status
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置organization.status
     *
     * @param status the value for organization.status
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取organization.create_time
     *
     * @return organization 的值.create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置organization.create_time
     *
     * @param createTime the value for organization.create_time
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取organization.update_time
     *
     * @return organization 的值.update_time
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置organization.update_time
     *
     * @param updateTime the value for organization.update_time
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取organization.create_by
     *
     * @return organization 的值.create_by
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * 设置organization.create_by
     *
     * @param createBy the value for organization.create_by
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * 获取organization.update_by
     *
     * @return organization 的值.update_by
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * 设置organization.update_by
     *
     * @param updateBy the value for organization.update_by
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 获取organization.enable
     *
     * @return organization 的值.enable
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 设置organization.enable
     *
     * @param enable the value for organization.enable
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    /**
     * 转字符
     *
     * @return String
     */
    @Override
    public String toString() {
        return new StringJoiner(", ", OrganizationPO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("orgName='" + orgName + "'")
                .add("orgType=" + orgType)
                .add("parentId=" + parentId)
                .add("status=" + status)
                .add("createTime=" + createTime)
                .add("updateTime=" + updateTime)
                .add("createBy=" + createBy)
                .add("updateBy=" + updateBy)
                .add("enable=" + enable)
                .toString();
    }

    public Organization toEntity() {
        Organization organization = new Organization();
        organization.setId(this.getId());
        organization.setOrgName(this.getOrgName());
        organization.setOrgType(this.getOrgType());
        organization.setParentId(this.getParentId());
        organization.setStatus(this.getStatus());
        organization.setCreateTime(this.getCreateTime());
        organization.setUpdateTime(this.getUpdateTime());
        organization.setCreateBy(this.getCreateBy());
        organization.setUpdateBy(this.getUpdateBy());
        // 注意：parentPath 和 subOrgList 在 OrganizationPO 类中没有对应的字段，因此这里不需要设置
        return organization;
    }

    public void fromEntity(Organization organization) {
        // 将Organization对象的id设置到OrganizationPO对象中
        this.setId(organization.getId());
        // 将Organization对象的orgName设置到OrganizationPO对象中
        this.setOrgName(organization.getOrgName());
        // 将Organization对象的orgType设置到OrganizationPO对象中
        this.setOrgType(organization.getOrgType());
        // 将Organization对象的parentId设置到OrganizationPO对象中
        this.setParentId(organization.getParentId());
        // 将Organization对象的status设置到OrganizationPO对象中
        this.setStatus(organization.getStatus());
        // 将Organization对象的createTime设置到OrganizationPO对象中
        this.setCreateTime(organization.getCreateTime());
        // 将Organization对象的updateTime设置到OrganizationPO对象中
        this.setUpdateTime(organization.getUpdateTime());
        // 将Organization对象的createBy设置到OrganizationPO对象中
        this.setCreateBy(organization.getCreateBy());
        // 将Organization对象的updateBy设置到OrganizationPO对象中
        this.setUpdateBy(organization.getUpdateBy());
        // 将Organization对象的enable设置到OrganizationPO对象中
        this.setEnable(organization.getEnable());
        // 返回填充好的OrganizationPO对象
    }

}