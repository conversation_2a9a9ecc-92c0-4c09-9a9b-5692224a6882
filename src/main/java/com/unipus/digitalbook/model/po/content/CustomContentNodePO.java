package com.unipus.digitalbook.model.po.content;

import com.unipus.digitalbook.model.entity.content.CustomContentNode;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomContentNodePO implements Serializable {
    String id;
    String text;
    String type;
    Long wordCount;
    Long cjkWordCount;
    Long nonCjkWordCount;
    Long audioDuration;
    Long videoDuration;
    String questionType;

    public CustomContentNodePO() {
        super();
    }

    public CustomContentNodePO(CustomContentNode node) {
        this.id = node.getId();
        this.text = node.getText();
        this.type = node.getType();
        this.wordCount = node.getWordCount();
        this.audioDuration = node.getAudioDuration();
        this.videoDuration = node.getVideoDuration();
        this.cjkWordCount = node.getCjkWordCount();
        this.nonCjkWordCount = node.getNonCjkWordCount();
        this.questionType = node.getQuestionType();
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getWordCount() {
        return wordCount;
    }

    public void setWordCount(Long wordCount) {
        this.wordCount = wordCount;
    }

    public Long getAudioDuration() {
        return audioDuration;
    }

    public void setAudioDuration(Long audioDuration) {
        this.audioDuration = audioDuration;
    }

    public Long getVideoDuration() {
        return videoDuration;
    }

    public void setVideoDuration(Long videoDuration) {
        this.videoDuration = videoDuration;
    }

    public Long getCjkWordCount() {
        return cjkWordCount;
    }

    public void setCjkWordCount(Long cjkWordCount) {
        this.cjkWordCount = cjkWordCount;
    }

    public Long getNonCjkWordCount() {
        return nonCjkWordCount;
    }

    public void setNonCjkWordCount(Long nonCjkWordCount) {
        this.nonCjkWordCount = nonCjkWordCount;
    }

    public String getQuestionType() {
        return questionType;
    }

    public void setQuestionType(String questionType) {
        this.questionType = questionType;
    }

    public CustomContentNode toEntity() {
        // 创建一个新的CustomContentNode对象
        CustomContentNode customContentNode = new CustomContentNode(this.getId(), this.getText(), this.getType());
        // 设置wordCount属性
        customContentNode.setWordCount(this.getWordCount());
        // 设置audioDuration属性
        customContentNode.setAudioDuration(this.getAudioDuration());
        // 设置videoDuration属性
        customContentNode.setVideoDuration(this.getVideoDuration());
        // 设置cjkWordCount和nonCjkWordCount属性
        customContentNode.setCjkWordCount(this.getCjkWordCount());
        // 设置nonCjkWordCount属性
        customContentNode.setNonCjkWordCount(this.getNonCjkWordCount());
        // 获取questionType属性
        customContentNode.setQuestionType(this.getQuestionType());
        return customContentNode;
    }

    public void fromEntity(CustomContentNode customContentNode) {
        this.setId(customContentNode.getId());
        this.setText(customContentNode.getText());
        this.setType(customContentNode.getType());
        this.setWordCount(customContentNode.getWordCount());
        this.setAudioDuration(customContentNode.getAudioDuration());
        this.setVideoDuration(customContentNode.getVideoDuration());
    }

}
