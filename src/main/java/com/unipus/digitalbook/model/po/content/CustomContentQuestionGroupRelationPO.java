package com.unipus.digitalbook.model.po.content;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/25 10:19
 */
/**
 * 自建内容与题组关系表
 */
public class CustomContentQuestionGroupRelationPO {
    /**
    * 主键ID
    */
    private Long id;

    /**
    * 关联的自建内容ID
    */
    private Long contentId;

    /**
    * 题组ID
    */
    private Long groupId;

    /**
    * 租户ID
    */
    private Long tenantId;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 最后更新时间
    */
    private Date updateTime;

    /**
    * 创建者ID
    */
    private Long createBy;

    /**
    * 最后更新者ID
    */
    private Long updateBy;

    /**
    * 是否有效 0-无效 1-有效
    */
    private Boolean enable;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getContentId() {
        return contentId;
    }

    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public CustomContentQuestionGroupRelationPO() {

    }

    public CustomContentQuestionGroupRelationPO(Long groupId, Long contentId, Long tenantId, Long userId) {
        this.groupId = groupId;
        this.contentId = contentId;
        this.tenantId = tenantId;
        this.createBy = userId;
        this.updateBy = userId;
    }
}