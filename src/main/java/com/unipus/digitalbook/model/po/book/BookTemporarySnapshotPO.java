package com.unipus.digitalbook.model.po.book;

import com.unipus.digitalbook.model.entity.book.BookTemporarySnapshot;

import java.io.Serializable;
import java.util.Date;

/**
 * 教材临时快照表
 *
 * @TableName book_temporary_snapshot
 */
public class BookTemporarySnapshotPO implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 教材ID
     */
    private String bookId;

    /**
     * 章节ID
     */
    private String chapterId;

    /**
     * 内容
     */
    private String content;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

    public BookTemporarySnapshotPO() {
    }

    public BookTemporarySnapshotPO(BookTemporarySnapshot bookTemporarySnapshot) {
        if (bookTemporarySnapshot == null) {
            return;
        }
        this.setId(bookTemporarySnapshot.getId());
        this.setBookId(bookTemporarySnapshot.getBookId());
        this.setChapterId(bookTemporarySnapshot.getChapterId());
        this.setContent(bookTemporarySnapshot.getContent());
        this.setCreateTime(bookTemporarySnapshot.getCreateTime());
        this.setUpdateTime(bookTemporarySnapshot.getUpdateTime());
    }

    public BookTemporarySnapshot toEntity() {
        BookTemporarySnapshot bookTemporarySnapshot = new BookTemporarySnapshot();
        bookTemporarySnapshot.setId(id);
        bookTemporarySnapshot.setBookId(bookId);
        bookTemporarySnapshot.setChapterId(chapterId);
        bookTemporarySnapshot.setContent(content);
        bookTemporarySnapshot.setCreateTime(createTime);
        bookTemporarySnapshot.setUpdateTime(updateTime);
        bookTemporarySnapshot.setCreateBy(createBy);
        return bookTemporarySnapshot;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }
}