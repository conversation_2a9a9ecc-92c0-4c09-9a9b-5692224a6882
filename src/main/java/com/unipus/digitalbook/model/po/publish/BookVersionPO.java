package com.unipus.digitalbook.model.po.publish;

import com.unipus.digitalbook.model.entity.publish.BookVersion;

import java.util.Date;

/**
 * 教材上架版本实体类
 * @TableName book_version
 */
public class BookVersionPO {
    // 发布版本 ID
    private Long id;
    // 教材 ID
    private String bookId;
    // 版本号
    private String versionNum;
    // 显示版本号
    private String showVersionNumber;
    // 排序
    private Integer sortOrder;
    // 状态 1：审核中，2：已发布，3：已驳回
    private Integer publishStatus;
    // 创建时间
    private Date createTime;
    // 最后更新时间
    private Date updateTime;
    // 创建者ID
    private Long createBy;

    // 最后更新者ID
    private Long updateBy;
    // 是否有效 0-无效 1-有效
    private Boolean enable;

    // 无参构造函数
    public BookVersionPO() {
    }

    public BookVersionPO(BookVersion bookVersion) {
        this.id = bookVersion.getId();
        this.bookId = bookVersion.getBookId();
        this.versionNum = bookVersion.getVersionNum();
        this.showVersionNumber = bookVersion.getShowVersionNumber();
        this.sortOrder = bookVersion.getSortOrder();
        this.publishStatus = bookVersion.getPublishStatus();
        this.createTime = bookVersion.getCreateTime();
        this.updateTime = bookVersion.getUpdateTime();
        this.createBy = bookVersion.getCreateBy();
        this.updateBy = bookVersion.getUpdateBy();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getVersionNum() {
        return versionNum;
    }

    public void setVersionNum(String versionNum) {
        this.versionNum = versionNum;
    }

    public Integer getPublishStatus() {
        return publishStatus;
    }

    public void setPublishStatus(Integer publishStatus) {
        this.publishStatus = publishStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public String getShowVersionNumber() {
        return showVersionNumber;
    }

    public void setShowVersionNumber(String showVersionNumber) {
        this.showVersionNumber = showVersionNumber;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    @Override
    public String toString() {
        return "BookVersionPO{" +
                "id=" + id +
                ", bookId=" + bookId +
                ", versionNum='" + versionNum + '\'' +
                ", publishStatus=" + publishStatus +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createBy=" + createBy +
                ", updateBy=" + updateBy +
                ", enable=" + enable +
                '}';
    }


    // toEntity 方法，将 BookVersionPO 转换为 BookVersion
    public BookVersion toEntity() {
        BookVersion bookVersion = new BookVersion();
        bookVersion.setId(this.id);
        bookVersion.setBookId(this.bookId);
        bookVersion.setVersionNum(this.versionNum);
        bookVersion.setShowVersionNumber(this.showVersionNumber);
        bookVersion.setSortOrder(this.sortOrder);
        bookVersion.setPublishStatus(this.publishStatus);
        bookVersion.setCreateTime(this.createTime);
        bookVersion.setUpdateTime(this.updateTime);
        bookVersion.setCreateBy(this.createBy);
        bookVersion.setUpdateBy(this.updateBy);
        bookVersion.setEnable(this.enable);
        return bookVersion;
    }
}
