package com.unipus.digitalbook.model.po;

import com.unipus.digitalbook.model.po.book.BookPublishItem;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *   表：book_publish_package
 *
 * <AUTHOR>
 * @date 2025年04月22日 11:26:40
 */
public class BookPublishPackagePO implements Serializable {
    /**
     *
     * 数据库字段： book_publish_package.id
     *
     * <AUTHOR>
     */
    private Long id;

    /**
     *
     * 数据库字段： book_publish_package.book_version_id
     *
     * <AUTHOR>
     */
    private Long bookVersionId;

    /**
     * 
     *  创建时间
     *
     * 数据库字段： book_publish_package.create_time
     *
     * <AUTHOR>
     */
    private Date createTime;

    /**
     * 
     *  最后更新时间
     *
     * 数据库字段： book_publish_package.update_time
     *
     * <AUTHOR>
     */
    private Date updateTime;

    /**
     * 
     *  创建者ID
     *
     * 数据库字段： book_publish_package.create_by
     *
     * <AUTHOR>
     */
    private Long createBy;

    /**
     * 
     *  最后更新者ID
     *
     * 数据库字段： book_publish_package.update_by
     *
     * <AUTHOR>
     */
    private Long updateBy;

    /**
     * 
     *  是否有效 0-无效 1-有效
     *
     * 数据库字段： book_publish_package.enable
     *
     * <AUTHOR>
     */
    private Boolean enable;

    /**
     * 
     *  变更数据包，包内根据type分类：
     *  1：教材基本信息
     *  2：教材简介
     *  3：版权信息
     *  4：配套资源
     *  5：教材章节
     *
     * 数据库字段： book_publish_package.publish_package
     *
     * <AUTHOR>
     */
    private List<BookPublishItem> publishPackage;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table book_publish_package
     *
     * <AUTHOR>
     */
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取book_publish_package.id
     *
     * @return  book_publish_package 的值.id
     *
     * <AUTHOR>
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置book_publish_package.id
     *
     * @param id the value for book_publish_package.id
     *
     * <AUTHOR>
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取book_publish_package.book_version_id
     *
     * @return  book_publish_package 的值.book_version_id
     *
     * <AUTHOR>
     */
    public Long getBookVersionId() {
        return bookVersionId;
    }

    /**
     * 设置book_publish_package.book_version_id
     *
     * @param bookVersionId the value for book_publish_package.book_version_id
     *
     * <AUTHOR>
     */
    public void setBookVersionId(Long bookVersionId) {
        this.bookVersionId = bookVersionId;
    }

    /**
     * 获取book_publish_package.create_time
     *
     * @return  book_publish_package 的值.create_time
     *
     * <AUTHOR>
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置book_publish_package.create_time
     *
     * @param createTime the value for book_publish_package.create_time
     *
     * <AUTHOR>
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取book_publish_package.update_time
     *
     * @return  book_publish_package 的值.update_time
     *
     * <AUTHOR>
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置book_publish_package.update_time
     *
     * @param updateTime the value for book_publish_package.update_time
     *
     * <AUTHOR>
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取book_publish_package.create_by
     *
     * @return  book_publish_package 的值.create_by
     *
     * <AUTHOR>
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * 设置book_publish_package.create_by
     *
     * @param createBy the value for book_publish_package.create_by
     *
     * <AUTHOR>
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * 获取book_publish_package.update_by
     *
     * @return  book_publish_package 的值.update_by
     *
     * <AUTHOR>
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * 设置book_publish_package.update_by
     *
     * @param updateBy the value for book_publish_package.update_by
     *
     * <AUTHOR>
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 获取book_publish_package.enable
     *
     * @return  book_publish_package 的值.enable
     *
     * <AUTHOR>
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 设置book_publish_package.enable
     *
     * @param enable the value for book_publish_package.enable
     *
     * <AUTHOR>
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    /**
     * 
     * 转字符 
     * 
     * @return String
     *
     * <AUTHOR>
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", bookVersionId=").append(bookVersionId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", enable=").append(enable);
        sb.append(", publishPackage=").append(publishPackage);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }

    public List<BookPublishItem> getPublishPackage() {
        return publishPackage;
    }

    public void setPublishPackage(List<BookPublishItem> publishPackage) {
        this.publishPackage = publishPackage;
    }
}