package com.unipus.digitalbook.model.po.chapter;

import com.unipus.digitalbook.model.entity.chapter.ChapterNode;
import lombok.Data;

import java.io.Serializable;
@Data
public class ChapterNodePO implements Serializable {
    String id;
    String text;
    String type;
    Long wordCount;
    Long cjkWordCount;
    Long nonCjkWordCount;
    Long audioDuration;
    Long videoDuration;
    String questionType;

    public ChapterNodePO() {
        super();
    }

    public ChapterNodePO(ChapterNode node){
        this.id = node.getId();
        this.text = node.getText();
        this.type = node.getType();
        this.wordCount = node.getWordCount();
        this.audioDuration = node.getAudioDuration();
        this.videoDuration = node.getVideoDuration();
        this.cjkWordCount = node.getCjkWordCount();
        this.nonCjkWordCount = node.getNonCjkWordCount();
        this.questionType = node.getQuestionType();
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getWordCount() {
        return wordCount;
    }

    public void setWordCount(Long wordCount) {
        this.wordCount = wordCount;
    }

    public Long getAudioDuration() {
        return audioDuration;
    }

    public void setAudioDuration(Long audioDuration) {
        this.audioDuration = audioDuration;
    }

    public Long getVideoDuration() {
        return videoDuration;
    }

    public void setVideoDuration(Long videoDuration) {
        this.videoDuration = videoDuration;
    }

    public Long getCjkWordCount() {
        return cjkWordCount;
    }

    public void setCjkWordCount(Long cjkWordCount) {
        this.cjkWordCount = cjkWordCount;
    }

    public Long getNonCjkWordCount() {
        return nonCjkWordCount;
    }

    public void setNonCjkWordCount(Long nonCjkWordCount) {
        this.nonCjkWordCount = nonCjkWordCount;
    }

    public String getQuestionType() {
        return questionType;
    }

    public void setQuestionType(String questionType) {
        this.questionType = questionType;
    }

    public ChapterNode toEntity() {
        // 创建一个新的ChapterNode对象
        ChapterNode chapterNode = new ChapterNode(this.getId(), this.getText(), this.getType());
        // 设置wordCount属性
        chapterNode.setWordCount(this.getWordCount());
        // 设置audioDuration属性
        chapterNode.setAudioDuration(this.getAudioDuration());
        // 设置videoDuration属性
        chapterNode.setVideoDuration(this.getVideoDuration());
        // 设置cjkWordCount和nonCjkWordCount属性
        chapterNode.setCjkWordCount(this.getCjkWordCount());
        // 设置nonCjkWordCount属性
        chapterNode.setNonCjkWordCount(this.getNonCjkWordCount());
        // 获取questionType属性
        chapterNode.setQuestionType(this.getQuestionType());
        return chapterNode;
    }

    public void fromEntity(ChapterNode chapterNode) {
        this.setId(chapterNode.getId());
        this.setText(chapterNode.getText());
        this.setType(chapterNode.getType());
        this.setWordCount(chapterNode.getWordCount());
        this.setAudioDuration(chapterNode.getAudioDuration());
        this.setVideoDuration(chapterNode.getVideoDuration());
    }

}
