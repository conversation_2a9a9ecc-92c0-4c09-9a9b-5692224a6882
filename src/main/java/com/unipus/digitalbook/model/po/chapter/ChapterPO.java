package com.unipus.digitalbook.model.po.chapter;

import com.unipus.digitalbook.model.entity.chapter.Chapter;

import java.io.Serializable;
import java.util.Date;

/**
 * 章节表
 * @TableName chapter
 */
public class ChapterPO implements Serializable {
    /**
     * 章节ID
     */
    private String id;

    /**
     * 关联教材ID
     */
    private String bookId;

    /**
     * 章节编号
     */
    private Integer chapterNumber;

    /**
     * 章节名称
     */
    private String name;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 ture 有效
     */
    private Boolean enable;

    public Chapter toEntity() {
        Chapter chapter = new Chapter();
        chapter.setId(id);
        chapter.setBookId(bookId);
        chapter.setChapterNumber(chapterNumber);
        chapter.setName(name);
        chapter.setCreateBy(createBy);
        chapter.setUpdateBy(updateBy);
        chapter.setCreateTime(createTime);
        chapter.setUpdateTime(updateTime);
        return chapter;
    }

    public ChapterPO fromEntity(Chapter chapter) {
        this.id = chapter.getId();
        this.bookId = chapter.getBookId();
        this.chapterNumber = chapter.getChapterNumber();
        this.name = chapter.getName();
        this.createBy = chapter.getCreateBy();
        this.updateBy = chapter.getUpdateBy();
        this.createTime = chapter.getCreateTime();
        this.updateTime = chapter.getUpdateTime();
        return this;
    }

    public Chapter toEntity(ChapterVersionPO chapterVersionPO) {
        Chapter chapter = toEntity();
        if (chapterVersionPO != null) {
            chapter.setChapterVersion(chapterVersionPO.toEntity());
        }
        return chapter;
    }
    /**
     * 章节ID
     */
    public String getId() {
        return id;
    }

    /**
     * 章节ID
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 关联教材ID
     */
    public String getBookId() {
        return bookId;
    }

    /**
     * 关联教材ID
     */
    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    /**
     * 章节编号
     */
    public Integer getChapterNumber() {
        return chapterNumber;
    }

    /**
     * 章节编号
     */
    public void setChapterNumber(Integer chapterNumber) {
        this.chapterNumber = chapterNumber;
    }

    /**
     * 章节名称
     */
    public String getName() {
        return name;
    }

    /**
     * 章节名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 创建者ID
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * 创建者ID
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * 最后更新者ID
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * 最后更新者ID
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 是否有效 ture 有效
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 是否有效 ture 有效
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }


    public void populateEntity(Chapter chapter){
        chapter.setId(this.getId());
        chapter.setBookId(this.getBookId());
        chapter.setChapterNumber(this.getChapterNumber());
        chapter.setName(this.getName());
        chapter.setCreateBy(this.getCreateBy());
        chapter.setUpdateBy(this.getUpdateBy());
        chapter.setCreateTime(this.getCreateTime());
        chapter.setUpdateTime(this.getUpdateTime());
    }
}