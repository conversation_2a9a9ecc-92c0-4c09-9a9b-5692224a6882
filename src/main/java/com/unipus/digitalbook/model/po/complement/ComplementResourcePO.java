package com.unipus.digitalbook.model.po.complement;

import com.unipus.digitalbook.model.entity.complement.ComplementResource;

import java.io.Serializable;
import java.util.Date;

/**
 * 教材配套资源
 * @TableName complement_resource
 */
public class ComplementResourcePO implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 资源id
     */
    private String resourceId;

    /**
     * 教材ID
     */
    private String bookId;

    /**
     * 资源类型 1-文档 2-视频 3-音频 4-图片
     */
    private Integer resourceType;

    /**
     * 媒体后缀
     */
    private String suffix;

    /**
     * 媒体名称
     */
    private String name;

    /**
     * 媒体地址
     */
    private String resourceUrl;

    /**
     * 媒体大小
     */
    private Long size;

    /**
     * 媒体时长
     */
    private Long duration;

    /**
     * 上传时间
     */
    private Date uploadTime;

    /**
     * 媒体可见状态 0-全部可见 1-仅教师可见
     */
    private Integer visibleStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 封面图
     */
    private String coverUrl;

    /**
     * 版本号
     */
    private String versionNumber;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;


    public ComplementResourcePO() {}

    public ComplementResourcePO(ComplementResource complementResource) {
        this.id = complementResource.getId();
        this.resourceId = complementResource.getResourceId();
        this.bookId = complementResource.getBookId();
        this.suffix = complementResource.getSuffix();
        this.resourceType = complementResource.getResourceType();
        this.name = complementResource.getName();
        this.resourceUrl = complementResource.getResourceUrl();
        this.size = complementResource.getSize();
        this.visibleStatus = complementResource.getVisibleStatus();
        this.coverUrl = complementResource.getCoverUrl();
        this.createBy = complementResource.getCreateBy();
        this.updateBy = complementResource.getUpdateBy();
        this.duration = complementResource.getDuration();
        this.versionNumber = complementResource.getVersionNumber();
    }

    public ComplementResource toEntity() {
        ComplementResource resource = new ComplementResource();
        resource.setId(this.id);
        resource.setResourceId(this.resourceId);
        resource.setBookId(this.bookId);
        resource.setSuffix(this.suffix);
        resource.setResourceType(this.resourceType);
        resource.setName(this.name);
        resource.setResourceUrl(this.resourceUrl);
        resource.setSize(this.size);
        resource.setVisibleStatus(this.visibleStatus);
        resource.setCoverUrl(this.coverUrl);
        resource.setUploadTime(this.uploadTime);
        resource.setCreateTime(this.createTime);
        resource.setUpdateTime(this.updateTime);
        resource.setCreateBy(this.createBy);
        resource.setUpdateBy(this.updateBy);
        resource.setVersionNumber(this.versionNumber);
        resource.setDuration(this.duration);
        return resource;
    }
    /**
     * 主键ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 主键ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 资源id
     */
    public String getResourceId() {
        return resourceId;
    }

    /**
     * 资源id
     */
    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    /**
     * 教材ID
     */
    public String getBookId() {
        return bookId;
    }

    /**
     * 教材ID
     */
    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    /**
     * 资源类型 1-文档 2-视频 3-音频 4-图片
     */
    public Integer getResourceType() {
        return resourceType;
    }

    /**
     * 资源类型 1-文档 2-视频 3-音频 4-图片
     */
    public void setResourceType(Integer resourceType) {
        this.resourceType = resourceType;
    }

    /**
     * 媒体后缀
     */
    public String getSuffix() {
        return suffix;
    }

    /**
     * 媒体后缀
     */
    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }

    /**
     * 媒体名称
     */
    public String getName() {
        return name;
    }

    /**
     * 媒体名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 媒体地址
     */
    public String getResourceUrl() {
        return resourceUrl;
    }

    /**
     * 媒体地址
     */
    public void setResourceUrl(String resourceUrl) {
        this.resourceUrl = resourceUrl;
    }

    /**
     * 媒体大小
     */
    public Long getSize() {
        return size;
    }

    /**
     * 媒体大小
     */
    public void setSize(Long size) {
        this.size = size;
    }

    /**
     * 上传时间
     */
    public Date getUploadTime() {
        return uploadTime;
    }

    /**
     * 上传时间
     */
    public void setUploadTime(Date uploadTime) {
        this.uploadTime = uploadTime;
    }

    /**
     * 媒体可见状态 0-全部可见 1-仅教师可见
     */
    public Integer getVisibleStatus() {
        return visibleStatus;
    }

    /**
     * 媒体可见状态 0-全部可见 1-仅教师可见
     */
    public void setVisibleStatus(Integer visibleStatus) {
        this.visibleStatus = visibleStatus;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 封面图
     */
    public String getCoverUrl() {
        return coverUrl;
    }

    /**
     * 封面图
     */
    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
    }

    /**
     * 最后更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 最后更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 创建者ID
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * 创建者ID
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * 最后更新者ID
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * 最后更新者ID
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 是否有效 0-无效 1-有效
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 是否有效 0-无效 1-有效
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }
}