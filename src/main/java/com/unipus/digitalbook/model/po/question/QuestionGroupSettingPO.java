package com.unipus.digitalbook.model.po.question;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.entity.question.QuestionSetting;

import java.io.Serializable;
import java.util.Date;

/**
 * @TableName question_group_setting
 */
public class QuestionGroupSettingPO implements Serializable {
    /**
     * 配置id
     */
    private Long id;

    /**
     * 题组ID
     */
    private Long groupId;

    /**
     * 配置内容
     */
    private String content;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

    /**
     * 配置id
     */
    public Long getId() {
        return id;
    }

    /**
     * 配置id
     */
    public void setId(Long id) {
        this.id = id;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    /**
     * 配置内容
     */
    public String getContent() {
        return content;
    }

    /**
     * 配置内容
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 最后更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 最后更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 创建者ID
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * 创建者ID
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * 最后更新者ID
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * 最后更新者ID
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 是否有效 0-无效 1-有效
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 是否有效 0-无效 1-有效
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public QuestionGroupSettingPO() {
    }

    /**
     * 将QuestionGroupSetting实体转换为PO
     * @param setting QuestionGroupSetting实体
     */
    public QuestionGroupSettingPO(QuestionSetting setting, Long groupId) {
        if (setting == null) {
            return;
        }
        this.content = JsonUtil.toJsonString(setting);
        this.enable = setting.getEnable();
        this.groupId = groupId;
        this.createBy = setting.getCreateBy();
        this.updateBy = setting.getUpdateBy();
    }

    /**
     * 将PO转换为QuestionGroupSetting实体
     *
     * @return QuestionGroupSetting实体
     */
    public QuestionSetting toEntity() {
        QuestionSetting setting = new QuestionSetting();
        setting.setId(this.id);
        setting.setCreateBy(this.createBy);
        setting.setUpdateBy(this.updateBy);
        setting.setEnable(this.enable);
        if (this.content != null) {
            QuestionSetting settingContent = JsonUtil.parseObject(this.content, QuestionSetting.class);
            if (settingContent != null) {
                setting.setAnswerType(settingContent.getAnswerType());
                setting.setPcLayoutType(settingContent.getPcLayoutType());
                setting.setAppLayoutType(settingContent.getAppLayoutType());
                setting.setAnswerRole(settingContent.getAnswerRole());
                setting.setAnswerTiming(settingContent.getAnswerTiming());
                setting.setAnswerLevel(settingContent.getAnswerLevel());
                setting.setAudioSetting(settingContent.getAudioSetting());
                setting.setVideoSetting(settingContent.getVideoSetting());
            }
        }
        return setting;
    }
}