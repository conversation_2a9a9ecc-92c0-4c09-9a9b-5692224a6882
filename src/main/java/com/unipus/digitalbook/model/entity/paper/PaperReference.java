package com.unipus.digitalbook.model.entity.paper;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@Schema(description = "试卷引用实体类")
public class PaperReference {
    @Schema(description = "试卷引用主键ID")
    private Long id;
    @Schema(description = "题组ID")
    private String paperId;
    @Schema(description = "试卷名称")
    private String paperName;
    @Schema(description = "试卷主键ID")
    private Long paperPrimaryId;
    @Schema(description = "教材ID")
    private String bookId;
    @Schema(description = "章节ID")
    private String chapterId;
    @Schema(description = "章节版本ID")
    private String chapterVersionId;
    @Schema(description = "试卷在章节里面的插入位置")
    private String position;
    @Schema(description = "是否启用")
    private Boolean enable;
    @Schema(description = "创建时间")
    private Date createTime;

    // 生成试卷引用唯一标识
    public String getUniqueKey(){
        return this.bookId + "_" + this.chapterId + "_" + this.paperId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getPaperName() {
        return paperName;
    }

    public void setPaperName(String paperName) {
        this.paperName = paperName;
    }

    public Long getPaperPrimaryId() {
        return paperPrimaryId;
    }

    public void setPaperPrimaryId(Long paperPrimaryId) {
        this.paperPrimaryId = paperPrimaryId;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getChapterVersionId() {
        return chapterVersionId;
    }

    public void setChapterVersionId(String chapterVersionId) {
        this.chapterVersionId = chapterVersionId;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
