package com.unipus.digitalbook.model.entity.question;

import java.math.BigDecimal;

/**
 * 业务上的一道小题
 * 实际上是一个题组，里面有一个或者多个题目
 */
public class SmallQuestion extends Question {

    public SmallQuestion() {
    }
    public SmallQuestion(Integer questionType) {
        this.setQuestionType(questionType);
    }
    @Override
    public BigDecimal score(UserAnswer userAnswers) {
        throw new UnsupportedOperationException("SmallQuestion不支持score方法");
    }

    @Override
    public double judge(UserAnswer userAnswer) {
        throw new UnsupportedOperationException("SmallQuestion不支持judge方法");
    }

    @Override
    public BigDecimal doScore(double accuracy) {
        throw new UnsupportedOperationException("BizQuestion不支持doScore方法");
    }

}
