package com.unipus.digitalbook.model.entity.question;

import com.unipus.digitalbook.model.enums.UserAnswerStatusEnum;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户题目解答
 */
public class UserAnswer {
    /**
     * 答案ID
     */
    private Long id;

    /**
     * 题目类型
     */
    private Integer questionType;

    /**
     * 题目业务ID
     */
    private String bizQuestionId;

    /**
     * 题目版本
     */
    private String questionVersionNumber;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 环境分区
     */
    private String envPartition;

    /**
     * 用户openId
     */
    private String openId;

    /**
     * 得分
     */
    private BigDecimal score;

    /**
     * 作答内容
     */
    private String answer;

    /**
     * 作答业务ID
     */
    private String bizAnswerId;

    /**
     * 评测结果
     */
    private String evaluation;

    /**
     * 作答批次
     */
    private String batchId;

    /**
     * 状态(0:未作答,1:错误,2:正确,3:半对)
     */
    private Integer status;

    /**
     * 作答是否通过
     */
    private Boolean pass;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

    /**
     * 判断字题是否正确
     * @return true-正确，false-错误
     */
    public Boolean isCorrect() {
        return this.status == UserAnswerStatusEnum.CORRECT.getCode();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getQuestionType() {
        return questionType;
    }

    public void setQuestionType(Integer questionType) {
        this.questionType = questionType;
    }

    public String getBizQuestionId() {
        return bizQuestionId;
    }

    public void setBizQuestionId(String bizQuestionId) {
        this.bizQuestionId = bizQuestionId;
    }

    public String getQuestionVersionNumber() {
        return questionVersionNumber;
    }

    public void setQuestionVersionNumber(String questionVersionNumber) {
        this.questionVersionNumber = questionVersionNumber;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public String getBizAnswerId() {
        return bizAnswerId;
    }

    public void setBizAnswerId(String bizAnswerId) {
        this.bizAnswerId = bizAnswerId;
    }

    public String getEvaluation() {
        return evaluation;
    }

    public void setEvaluation(String evaluation) {
        this.evaluation = evaluation;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Boolean getPass() {
        return pass;
    }

    public void setPass(Boolean pass) {
        this.pass = pass;
    }

    public String getEnvPartition() {
        return envPartition;
    }

    public void setEnvPartition(String envPartition) {
        this.envPartition = envPartition;
    }
}


