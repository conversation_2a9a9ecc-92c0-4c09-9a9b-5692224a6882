package com.unipus.digitalbook.model.entity.question.type;

import com.unipus.digitalbook.model.entity.question.IQuestion;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.UserAnswer;

import java.math.BigDecimal;

/**
 * 多媒体上传题
 */
public class TranslationQuestion extends Question implements IQuestion {
    @Override
    public BigDecimal doScore(double accuracy) {
        return getScore().multiply(BigDecimal.valueOf(accuracy));
    }

    @Override
    public double judge(UserAnswer userAnswer) {
        return getJudgeStrategy().judge(this, userAnswer);
    }
}
