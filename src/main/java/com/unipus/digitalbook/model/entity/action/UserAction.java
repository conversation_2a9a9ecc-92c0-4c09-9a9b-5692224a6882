package com.unipus.digitalbook.model.entity.action;

import com.unipus.digitalbook.model.enums.ContentTypeEnum;

public class UserAction {
    /**
     * 内容ID
     */
    private String contentId;
    /**
     * 内容版本id
     */
    private Long contentVersionId;

    /**
     * 节点ID
     */
    private String nodeId;

    /**
     * 题目ID
     */
    private String questionId;

    /**
     * 用户ID
     */
    private String openId;
    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 用户IP
     */
    private String ip;

    /**
     * 数据包
     */
    private String dataPackage;

    /**
     * 环境分区
     */
    private String envPartition;
    /**
     * 行为开始时间
     */
    private Long a;

    /**
     * 行为结束时间
     */
    private Long b;

    /**
     * 内容类型
     */
    private ContentTypeEnum contentType;

    /**
     * 教材ID
     */
    private String bookId;

    /**
     * 教材版本号
     */
    private String bookVersionNumber;
    /**
     * 行为时长
     */
    public Long getDuration() {
        if (b == null || a == null) {
            return null;
        }
        return b - a;
    }

    public String getContentId() {
        return contentId;
    }

    public void setContentId(String contentId) {
        this.contentId = contentId;
    }

    public Long getContentVersionId() {
        return contentVersionId;
    }

    public void setContentVersionId(Long contentVersionId) {
        this.contentVersionId = contentVersionId;
    }
    public Long getA() {
        return a;
    }

    public void setA(Long a) {
        this.a = a;
    }

    public Long getB() {
        return b;
    }

    public void setB(Long b) {
        this.b = b;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getDataPackage() {
        return dataPackage;
    }

    public void setDataPackage(String dataPackage) {
        this.dataPackage = dataPackage;
    }

    public String getEnvPartition() {
        return envPartition;
    }

    public void setEnvPartition(String envPartition) {
        this.envPartition = envPartition;
    }

    public ContentTypeEnum getContentType() {
        return contentType;
    }

    public void setContentType(ContentTypeEnum contentType) {
        this.contentType = contentType;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public String getQuestionId() {
        return questionId;
    }

    public void setQuestionId(String questionId) {
        this.questionId = questionId;
    }


    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getBookVersionNumber() {
        return bookVersionNumber;
    }

    public void setBookVersionNumber(String bookVersionNumber) {
        this.bookVersionNumber = bookVersionNumber;
    }
}
