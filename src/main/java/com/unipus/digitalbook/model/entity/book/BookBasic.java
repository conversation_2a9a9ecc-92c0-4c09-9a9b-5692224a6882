package com.unipus.digitalbook.model.entity.book;

import com.unipus.digitalbook.common.utils.HexUtil;
import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.model.entity.Series;

import java.util.Date;

/**
 * 教材基本信息实体
 */
public class BookBasic extends BookNode implements BookInfoInterface{
    private Long id;

    /**
     * 教材中文名称
     */
    private String chineseName;

    /**
     * 教材英文名称
     */
    private String englishName;

    /**
     * 语种
     */
    private String language;

    /**
     * 教材业务类型
     */
    private String businessType;

    /**
     * 教程系列
     */
    private Long seriesId;

    /**
     * 教材系列
     */
    private Series series;

    /**
     * 对应课程
     */
    private String course;

    /**
     * 课程性质
     */
    private String courseNature;

    /**
     * 适用专业
     */
    private String applicableMajor;

    /**
     * 适用年级
     */
    private String applicableGrade;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 联系邮箱
     */
    private String contactEmail;

    /**
     * PC端封面图片地址
     */
    private String pcCoverUrl;

    /**
     * APP横版封面图片地址
     */
    private String appHorizontalCoverUrl;

    /**
     * APP竖版封面图片地址
     */
    private String appVerticalCoverUrl;

    /**
     * 浅色
     */
    private String lightColor;

    /**
     * 深色
     */
    private String darkColor;

    /**
     * 版本号
     */
    private String versionNumber;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

    /**
     * 是否纯数字教材  0-否 1-是
     */
    private Boolean digitalFlag;

    /**
     * 教材简介的md5值
     */
    private String hashCode;

    public BookBasic generateMd5() {
        String result = String.join(",", this.chineseName, this.englishName, this.language, this.businessType,
                String.valueOf(this.seriesId), this.course, this.courseNature, this.applicableMajor,
                this.applicableGrade, this.contactPhone, this.contactEmail, this.pcCoverUrl, this.appHorizontalCoverUrl,
                this.appVerticalCoverUrl, this.lightColor, this.darkColor, String.valueOf(this.digitalFlag));
        this.hashCode = HexUtil.generateObjectHash(result);
        return this;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getChineseName() {
        return chineseName;
    }

    public void setChineseName(String chineseName) {
        this.chineseName = chineseName;
    }

    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public Long getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(Long seriesId) {
        this.seriesId = seriesId;
    }

    public String getCourse() {
        return course;
    }

    public void setCourse(String course) {
        this.course = course;
    }

    public String getCourseNature() {
        return courseNature;
    }

    public void setCourseNature(String courseNature) {
        this.courseNature = courseNature;
    }

    public String getApplicableMajor() {
        return applicableMajor;
    }

    public void setApplicableMajor(String applicableMajor) {
        this.applicableMajor = applicableMajor;
    }

    public String getApplicableGrade() {
        return applicableGrade;
    }

    public void setApplicableGrade(String applicableGrade) {
        this.applicableGrade = applicableGrade;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getPcCoverUrl() {
        return pcCoverUrl;
    }

    public void setPcCoverUrl(String pcCoverUrl) {
        this.pcCoverUrl = pcCoverUrl;
    }

    public String getAppHorizontalCoverUrl() {
        return appHorizontalCoverUrl;
    }

    public void setAppHorizontalCoverUrl(String appHorizontalCoverUrl) {
        this.appHorizontalCoverUrl = appHorizontalCoverUrl;
    }

    public String getAppVerticalCoverUrl() {
        return appVerticalCoverUrl;
    }

    public void setAppVerticalCoverUrl(String appVerticalCoverUrl) {
        this.appVerticalCoverUrl = appVerticalCoverUrl;
    }

    public String getLightColor() {
        return lightColor;
    }

    public void setLightColor(String lightColor) {
        this.lightColor = lightColor;
    }

    public String getDarkColor() {
        return darkColor;
    }

    public void setDarkColor(String darkColor) {
        this.darkColor = darkColor;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Boolean getDigitalFlag() {
        return digitalFlag;
    }

    public void setDigitalFlag(Boolean digitalFlag) {
        this.digitalFlag = digitalFlag;
    }

    public String getHashCode() {
        return hashCode;
    }

    public void setHashCode(String hashCode) {
        this.hashCode = hashCode;
    }

    public Series getSeries() {
        return series;
    }

    public void setSeries(Series series) {
        this.series = series;
    }

    public BookBasic deepCopy() {
        BookBasic copy = new BookBasic();

        // 复制父类 BookNode 的属性
        copy.setBookId(getBookId());
        // 复制基本类型和不可变对象
        copy.setChineseName(this.chineseName);
        copy.setEnglishName(this.englishName);
        copy.setLanguage(this.language);
        copy.setBusinessType(this.businessType);
        copy.setSeriesId(this.seriesId);
        copy.setCourse(this.course);
        copy.setCourseNature(this.courseNature);
        copy.setApplicableMajor(this.applicableMajor);
        copy.setApplicableGrade(this.applicableGrade);
        copy.setContactPhone(this.contactPhone);
        copy.setContactEmail(this.contactEmail);
        copy.setPcCoverUrl(this.pcCoverUrl);
        copy.setAppHorizontalCoverUrl(this.appHorizontalCoverUrl);
        copy.setAppVerticalCoverUrl(this.appVerticalCoverUrl);
        copy.setLightColor(this.lightColor);
        copy.setDarkColor(this.darkColor);
        // 版本号使用当前时间戳进行编码
        copy.setVersionNumber(IdentifierUtil.generateVersion());
        copy.setEnable(this.enable);
        copy.setDigitalFlag(this.digitalFlag);
        copy.setHashCode(this.hashCode);
        copy.setSeries(this.series);
        return copy;
    }
}