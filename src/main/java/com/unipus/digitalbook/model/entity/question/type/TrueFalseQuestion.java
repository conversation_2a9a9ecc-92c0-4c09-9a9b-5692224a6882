package com.unipus.digitalbook.model.entity.question.type;

import com.unipus.digitalbook.model.entity.question.IQuestion;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.UserAnswer;

import java.math.BigDecimal;

/**
 * 判断题
 */
public class TrueFalseQuestion extends Question implements IQuestion {

    @Override
    public BigDecimal doScore(double accuracy) {
        if (accuracy == 1) {
            return getScore();
        }
        return BigDecimal.ZERO;
    }

    @Override
    public double judge(UserAnswer userAnswer) {
        return getJudgeStrategy().judge(this, userAnswer);
    }
}
