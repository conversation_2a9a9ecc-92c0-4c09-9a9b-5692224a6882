package com.unipus.digitalbook.model.entity.complement;

import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.model.entity.book.BookInfoInterface;
import com.unipus.digitalbook.model.entity.book.BookNode;

import java.util.Date;

/**
 * 教材配套资源
 */
public class ComplementResource extends BookNode implements BookInfoInterface {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 资源id
     */
    private String resourceId;

    /**
     * 资源类型 1-文档 2-视频 3-音频 4-图片
     */
    private Integer resourceType;

    /**
     * 媒体后缀
     */
    private String suffix;

    /**
     * 媒体名称
     */
    private String name;

    /**
     * 媒体地址
     */
    private String resourceUrl;

    /**
     * 媒体大小
     */
    private Long size;

    /**
     * 媒体时长
     */
    private Long duration;

    /**
     * 上传时间
     */
    private Date uploadTime;

    /**
     * 媒体可见状态 0-全部可见 1-仅教师可见
     */
    private Integer visibleStatus;

    /**
     * 版本号
     */
    private String versionNumber;
    /**
     * 封面地址
     */
    private String coverUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public Integer getResourceType() {
        return resourceType;
    }

    public void setResourceType(Integer resourceType) {
        this.resourceType = resourceType;
    }

    public String getSuffix() {
        return suffix;
    }

    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getResourceUrl() {
        return resourceUrl;
    }

    public void setResourceUrl(String resourceUrl) {
        this.resourceUrl = resourceUrl;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public Date getUploadTime() {
        return uploadTime;
    }

    public void setUploadTime(Date uploadTime) {
        this.uploadTime = uploadTime;
    }

    public Integer getVisibleStatus() {
        return visibleStatus;
    }

    public void setVisibleStatus(Integer visibleStatus) {
        this.visibleStatus = visibleStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public String getCoverUrl() {
        return coverUrl;
    }

    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }


    public ComplementResource deepCopy() {
        ComplementResource copy = new ComplementResource();
        copy.setResourceId(this.getResourceId());
        copy.setResourceType(this.getResourceType());
        copy.setSuffix(this.getSuffix());
        copy.setName(this.getName());
        copy.setResourceUrl(this.getResourceUrl());
        copy.setSize(this.getSize());
        copy.setDuration(this.getDuration());
        copy.setUploadTime(new Date(this.getUploadTime().getTime()));
        copy.setVisibleStatus(this.getVisibleStatus());
        copy.setCoverUrl(this.getCoverUrl());
        copy.setBookId(this.getBookId());
        copy.setVersionNumber(IdentifierUtil.generateVersion());
        return copy;
    }


}
