package com.unipus.digitalbook.model.entity.question;

import com.unipus.digitalbook.model.enums.LearnTypeEnum;

import java.util.List;

public class UserAnswerNodeData {
    /**
     * 答题人ID
     */
    private String openId;

    /**
     * 教材ID
     */
    private String bookId;

    /**
     * 教材版本号
     */
    private String bookVersionNumber;

    /**
     * 内容ID
     */
    private String contentId;

    /**
     * 答题人IP
     */
    private String ip;

    /**
     * 学习类型
     */
    private String learnType;
    /**
     * 答题数据
     */
    private UserAnswerData data;

    public UserAnswerNodeData(){}

    public UserAnswerNodeData(SubmitAnswerContext context, BigQuestionGroup question, List<UserAnswer> userAnswers) {
        this.learnType = LearnTypeEnum.QUESTION.getCode();
        this.openId = context.getOpenId();
        this.bookId = context.getBookId();
        this.bookVersionNumber = context.getBookVersionNumber();
        this.contentId = context.getContentId();
        this.ip = context.getClientIp();
        this.data = new UserAnswerData(question, userAnswers, context.getDataPackage());
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public UserAnswerData getData() {
        return data;
    }

    public void setData(UserAnswerData data) {
        this.data = data;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getBookVersionNumber() {
        return bookVersionNumber;
    }

    public void setBookVersionNumber(String bookVersionNumber) {
        this.bookVersionNumber = bookVersionNumber;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getLearnType() {
        return learnType;
    }

    public void setLearnType(String learnType) {
        this.learnType = learnType;
    }

    public String getContentId() {
        return contentId;
    }

    public void setContentId(String contentId) {
        this.contentId = contentId;
    }
}
