package com.unipus.digitalbook.model.entity.book;

import java.util.Date;

public class BookUserActivity {
    private Long id;
    private Long userId;
    private String bookId;
    private Integer activityType;
    private Date activityTime;

    private Boolean enable;
    private Long opsUserId;

    public BookUserActivity(Long userId, String bookId, Integer activityType, Date activityTime, Long opsUserId, boolean enable) {
        this.userId = userId;
        this.bookId = bookId;
        this.activityType = activityType;
        this.activityTime = activityTime;
        this.opsUserId = opsUserId;
        this.enable = enable;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public Integer getActivityType() {
        return activityType;
    }

    public void setActivityType(Integer activityType) {
        this.activityType = activityType;
    }

    public Date getActivityTime() {
        return activityTime;
    }

    public void setActivityTime(Date activityTime) {
        this.activityTime = activityTime;
    }

    public Long getOpsUserId() {
        return opsUserId;
    }

    public void setOpsUserId(Long opsUserId) {
        this.opsUserId = opsUserId;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }
}
