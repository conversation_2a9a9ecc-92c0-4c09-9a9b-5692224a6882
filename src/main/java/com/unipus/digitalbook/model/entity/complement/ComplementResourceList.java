package com.unipus.digitalbook.model.entity.complement;

import com.unipus.digitalbook.common.utils.HexUtil;
import com.unipus.digitalbook.model.entity.book.BookNode;

import java.util.List;
import java.util.stream.Collectors;

public class ComplementResourceList extends BookNode {
    /**
     * 资源列表
     */
    private List<ComplementResource> complementResourceList;

    /**
     * 教材下所有配套资源该版本资源的总数量
     */
    private Integer totalCount;

    /**
     * 教材下所有配套资源该版本资源的md5值
     */
    private String hashCode;

    /**
     * 教材下所有配套资源的版本号
     */
    private String versionNumber;

    public ComplementResourceList(){}

    public ComplementResourceList(List<ComplementResource> complementResourceList, Integer totalCount) {
        this.complementResourceList = complementResourceList;
        this.totalCount = totalCount;
    }

    public ComplementResourceList(List<ComplementResource> complementResourceList, String versionNumber) {
        this.complementResourceList = complementResourceList;
        this.totalCount = complementResourceList.size();
        this.versionNumber = versionNumber;
    }

    public ComplementResourceList generateMd5() {
        String result = complementResourceList.stream().map(r ->
                        String.join(",", r.getName(), r.getResourceUrl(), String.valueOf(r.getVisibleStatus())))
                .collect(Collectors.joining());
        this.hashCode = HexUtil.generateObjectHash(result);
        return this;
    }


    public List<ComplementResource> getComplementResourceList() {
        return complementResourceList;
    }

    public void setComplementResourceList(List<ComplementResource> complementResourceList) {
        this.complementResourceList = complementResourceList;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public String getHashCode() {
        return hashCode;
    }

    public void setHashCode(String hashCode) {
        this.hashCode = hashCode;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }
}
