package com.unipus.digitalbook.model.entity.book;

import java.util.Date;

public class BookFile {
    /**
     * 资源唯一ID (主键)
     */
    private Long id;

    /**
     * 关联书籍的ID
     */
    private String bookId;

    /**
     * 资源名称（例如：原始文件名或用户自定义名称）
     */
    private String resourceName;

    /**
     * 资源的存储路径或URL
     */
    private String storagePath;

    /**
     * 资源类型 (例如：IMAGE, AUDIO, VIDEO, DOCUMENT)
     */
    private String resourceType;

    /**
     * MIME类型 (例如：image/jpeg, audio/mpeg, application/pdf)
     */
    private String mimeType;

    /**
     * 文件大小（字节）
     */
    private Long fileSizeBytes;

    /**
     * 上传者用户ID (如果需要追踪上传者，可以关联用户表)
     */
    private Long uploaderId;

    /**
     * 资源状态 (例如：ACTIVE, DELETED, PENDING)
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

    /**
     * 资源描述或备注 (可选)
     */
    private String description;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String  bookId) {
        this.bookId = bookId;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public String getStoragePath() {
        return storagePath;
    }

    public void setStoragePath(String storagePath) {
        this.storagePath = storagePath;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    public Long getFileSizeBytes() {
        return fileSizeBytes;
    }

    public void setFileSizeBytes(Long fileSizeBytes) {
        this.fileSizeBytes = fileSizeBytes;
    }

    public Long getUploaderId() {
        return uploaderId;
    }

    public void setUploaderId(Long uploaderId) {
        this.uploaderId = uploaderId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}

