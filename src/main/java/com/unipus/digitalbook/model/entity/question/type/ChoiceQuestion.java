package com.unipus.digitalbook.model.entity.question.type;

import com.unipus.digitalbook.model.entity.question.IQuestion;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.UserAnswer;

import java.math.BigDecimal;

/**
 * 选择题
 * 单选或者多选
 */
public class ChoiceQuestion extends Question implements IQuestion {

    @Override
    public BigDecimal doScore(double accuracy) {
        if (accuracy == 1) {
            return getScore();
        }
        if (accuracy == 0) {
            return BigDecimal.ZERO;
        }
        return getScore().multiply(BigDecimal.valueOf(0.5));
    }

    @Override
    public double judge(UserAnswer userAnswer) {
        return getJudgeStrategy().judge(this, userAnswer);
    }
}
