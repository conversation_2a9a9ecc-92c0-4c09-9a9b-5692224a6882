package com.unipus.digitalbook.model.entity;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户登录信息缓存实体类
 * 用户权限信息变更时需要重新加载
 */
@Schema(description = "权限系统-缓存用户信息")
public class CurrentUserInfo implements Serializable {

    @Schema(description = "用户信息")
    private UserInfo userInfo;

    @Schema(description = "所属组织信息")
    private List<OrgInfo> orgInfoList;

    @Schema(description = "用户机构/角色映射")
    private Map<Long, Set<Long>> orgRolesMap;

    @Schema(description = "用户登录时间")
    private Date lastLoginTime;

    @Schema(description = "是否已激活: false-未激活 true-已激活")
    private Boolean active;

    public CurrentUserInfo() {}

    public UserInfo getUserInfo() {
        return userInfo;
    }

    public CurrentUserInfo setUserInfo(UserInfo userInfo) {
        this.userInfo = userInfo;
        return this;
    }

    public Map<Long, Set<Long>> getOrgRolesMap() {
        return orgRolesMap;
    }

    public CurrentUserInfo setOrgRolesMap(Map<Long, Set<Long>> orgRolesMap) {
        this.orgRolesMap = orgRolesMap;
        return this;
    }

    public Date getLastLoginTime() {
        return lastLoginTime;
    }

    public CurrentUserInfo setLastLoginTime(Date lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
        return this;
    }

    public List<OrgInfo> getOrgInfoList() {
        return orgInfoList;
    }

    public CurrentUserInfo setOrgInfoList(List<OrgInfo> orgInfos) {
        this.orgInfoList = orgInfos;
        return this;
    }

    public Boolean getActive() {
        return this.active;
    }

    public CurrentUserInfo setActive(Boolean active) {
        this.active = active;
        return this;
    }

    public static CurrentUserInfo build(UserInfo userInfo, List<OrgInfo> orgInfos,
                                        Map<Long, Set<Long>> orgRolesMap, Date lastLoginTime, Boolean active) {
        return new CurrentUserInfo()
                .setUserInfo(userInfo)
                .setOrgInfoList(orgInfos)
                .setOrgRolesMap(orgRolesMap)
                .setLastLoginTime(lastLoginTime)
                .setActive(active);
    }
}