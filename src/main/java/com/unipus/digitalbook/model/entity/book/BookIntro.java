package com.unipus.digitalbook.model.entity.book;

import com.unipus.digitalbook.common.utils.HexUtil;
import com.unipus.digitalbook.common.utils.IdentifierUtil;

import java.util.Date;

/**
 * 教材简介实体
 */
public class BookIntro extends BookNode implements BookInfoInterface{

    /**
     * ID
     */
    private Long id;

    /**
     * 教材详细介绍
     */
    private String description;

    /**
     * 版本号
     */
    private String versionNumber;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

    /**
     * 教材简介的md5值
     */
    private String hashCode;

    public BookIntro generateMd5() {
        String result = String.join(",", this.description);
        this.hashCode = HexUtil.generateObjectHash(result);
        return this;
    }



    /**
     * 关联教材ID
     */
    public String getBookId() {
        return bookId;
    }

    /**
     * 关联教材ID
     */
    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    /**
     * 教材详细介绍
     */
    public String getDescription() {
        return description;
    }

    /**
     * 教材详细介绍
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 版本号
     */
    public String getVersionNumber() {
        return versionNumber;
    }

    /**
     * 版本号
     */
    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 最后更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 最后更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 创建者ID
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * 创建者ID
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * 最后更新者ID
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * 最后更新者ID
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 是否有效 0-无效 1-有效
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 是否有效 0-无效 1-有效
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public String getHashCode() {
        return hashCode;
    }

    public void setHashCode(String hashCode) {
        this.hashCode = hashCode;
    }


    public BookIntro deepCopy() {
        BookIntro copy = new BookIntro();
        copy.setBookId(this.getBookId());
        copy.setDescription(this.getDescription());
        // 版本号使用当前时间戳进行编码
        copy.setVersionNumber(IdentifierUtil.generateVersion());
        copy.setCreateTime(this.getCreateTime() != null ? (Date) this.getCreateTime().clone() : null);
        copy.setUpdateTime(this.getUpdateTime() != null ? (Date) this.getUpdateTime().clone() : null);
        copy.setEnable(this.getEnable());
        copy.setHashCode(this.getHashCode());
        return copy;
    }


    @Override
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}