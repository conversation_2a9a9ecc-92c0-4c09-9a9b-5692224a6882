package com.unipus.digitalbook.model.entity.question;

import com.unipus.digitalbook.model.entity.tag.Tag;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * 标签
 *
 * <AUTHOR>
 */
public class QuestionTag {
    /**
     * 标签id
     */
    private Long id;
    /**
     * 父级标签id
     */
    private Long parentId;

    /**
     * 标签类型
     * @see  com.unipus.digitalbook.model.enums.TagTypeEnum
     */
    private Integer tagType;
    /**
     * 标签名称
     */
    private String name;

    public QuestionTag() {}

    public QuestionTag(Tag tag) {
        this.id = tag.getTagId();
        this.parentId = tag.getParentId();
        this.tagType = tag.getTagType();
        this.name = tag.getTagName();
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getTagType() {
        return tagType;
    }

    public void setTagType(Integer tagType) {
        this.tagType = tagType;
    }

    public static List<Tag> toTagList(List<List<QuestionTag>> tags) {
        return tags.stream().flatMap(Collection::stream).map(questionTag -> {
                Tag tag = new Tag();
                tag.setTagId(questionTag.getId());
                tag.setParentId(questionTag.getParentId());
                tag.setTagName(questionTag.getName());
                tag.setTagType(questionTag.getTagType());
                return tag;
            }).toList();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        QuestionTag tag = (QuestionTag) o;
        return Objects.equals(parentId, tag.parentId) &&
                Objects.equals(name, tag.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(parentId, name);
    }

}
