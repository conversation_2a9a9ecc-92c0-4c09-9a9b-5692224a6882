package com.unipus.digitalbook.model.entity.qrcode;

import java.util.Date;

/**
 * 表示二维码实体类的详细信息。
 */
public class QrCode {
    /**
     * 二维码ID，唯一标识一个二维码。
     */
    private Long id;

    /**
     * 二维码短链地址，扫描二维码后跳转的目标URL。
     */
    private String qrCodeUrl;

    /**
     * 二维码尺寸
     */
    private String qrCodeSize;

    /**
     * 实体教材位置，记录教材在物理存储中的具体位置。
     */
    private String realBookLocation;

    /**
     * 教材名称，表示该二维码关联的教材的名称。
     */
    private String bookName;

    /**
     * 教材ID，所属教材的唯一标识教材。
     */
    private String bookId;

    /**
     * 教材内部链接，可能用于在教材内容中导航到特定章节或页面。
     */
    private String bookInnerUrl;

    /**
     * 链接验证状态，指示二维码链接是否已经验证通过。
     */
    private Boolean linkVerificationStatus;

    /**
     * 链接验证时间，记录链接最后一次验证的时间戳。
     */
    private Date linkVerificationTime;

    /**
     * 备注，用于记录二维码的附加信息或说明。
     */
    private String remarks;

    /**
     * 创建时间，记录二维码创建的时间戳。
     */
    private Date createTime;

    /**
     * 最后更新时间，记录二维码最后更新的时间戳。
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效，指示二维码是否有效。值为0时表示无效，值为1时表示有效。
     */
    private Boolean enable;


    /**
     * 获取二维码的唯一标识符ID。
     *
     * @return 二维码的ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置二维码的唯一标识符ID。
     *
     * @param id 二维码的ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取二维码的链接地址。
     *
     * @return 二维码的链接地址
     */
    public String getQrCodeUrl() {
        return qrCodeUrl;
    }

    /**
     * 设置二维码的链接地址。
     *
     * @param qrCodeUrl 二维码的链接地址
     */
    public void setQrCodeUrl(String qrCodeUrl) {
        this.qrCodeUrl = qrCodeUrl;
    }

    public String getQrCodeSize() {
        return qrCodeSize;
    }

    public void setQrCodeSize(String qrCodeSize) {
        this.qrCodeSize = qrCodeSize;
    }

    /**
     * 获取实体教材的位置。
     *
     * @return 实体教材的位置
     */
    public String getRealBookLocation() {
        return realBookLocation;
    }

    /**
     * 设置实体教材的位置。
     *
     * @param realBookLocation 实体教材的位置
     */
    public void setRealBookLocation(String realBookLocation) {
        this.realBookLocation = realBookLocation;
    }

    /**
     * 获取教材名称。
     *
     * @return 教材名称
     */
    public String getBookName() {
        return bookName;
    }

    /**
     * 设置教材名称。
     *
     * @param bookName 教材名称
     */
    public void setBookName(String bookName) {
        this.bookName = bookName;
    }

    /**
     * 获取所属教材的ID。
     *
     * @return 所属教材的ID
     */
    public String getBookId() {
        return bookId;
    }

    /**
     * 设置所属教材的ID。
     *
     * @param bookId 所属教材的ID
     */
    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    /**
     * 获取教材内部链接。
     *
     * @return 教材内部链接
     */
    public String getBookInnerUrl() {
        return bookInnerUrl;
    }

    /**
     * 设置教材内部链接。
     *
     * @param bookInnerUrl 教材内部链接
     */
    public void setBookInnerUrl(String bookInnerUrl) {
        this.bookInnerUrl = bookInnerUrl;
    }

    /**
     * 获取链接验证状态。
     *
     * @return 链接验证状态
     */
    public Boolean getLinkVerificationStatus() {
        return linkVerificationStatus;
    }

    /**
     * 设置链接验证状态。
     *
     * @param linkVerificationStatus 链接验证状态
     */
    public void setLinkVerificationStatus(Boolean linkVerificationStatus) {
        this.linkVerificationStatus = linkVerificationStatus;
    }

    /**
     * 获取链接验证时间。
     *
     * @return 链接验证时间
     */
    public Date getLinkVerificationTime() {
        return linkVerificationTime;
    }

    /**
     * 设置链接验证时间。
     *
     * @param linkVerificationTime 链接验证时间
     */
    public void setLinkVerificationTime(Date linkVerificationTime) {
        this.linkVerificationTime = linkVerificationTime;
    }

    /**
     * 获取备注信息。
     *
     * @return 备注信息
     */
    public String getRemarks() {
        return remarks;
    }

    /**
     * 设置备注信息。
     *
     * @param remarks 备注信息
     */
    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    /**
     * 获取二维码的创建时间。
     *
     * @return 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置二维码的创建时间。
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取二维码的最后更新时间。
     *
     * @return 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置二维码的最后更新时间。
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 获取二维码的状态。
     *
     * @return 二维码的状态
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 设置二维码的状态。
     *
     * @param enable 二维码的状态
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    /**
     * 返回QrCode对象的字符串表示形式，包含所有属性的值。
     *
     * @return QrCode对象的字符串表示形式
     */
    @Override
    public String toString() {
        return "QrCode{" +
                "id=" + id +
                ", qrCodeUrl='" + qrCodeUrl + '\'' +
                ", qrCodeSize='" + qrCodeSize + '\'' +
                ", realBookLocation='" + realBookLocation + '\'' +
                ", bookName='" + bookName + '\'' +
                ", bookId='" + bookId + '\'' +
                ", bookInnerUrl='" + bookInnerUrl + '\'' +
                ", linkVerificationStatus=" + linkVerificationStatus +
                ", linkVerificationTime=" + linkVerificationTime +
                ", remarks='" + remarks + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createBy=" + createBy +
                ", updateBy=" + updateBy +
                ", enable=" + enable +
                '}';
    }
}


