package com.unipus.digitalbook.model.entity.clio;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/8 13:46
 */
@Data
public class ClioSig {

    @Schema(description = "账号信息")
    private String applicationId;

    @Schema(description = "数字签名")
    private Long timestamp;

    @Schema(description = "unix 时间戳（10位）")
    private String sig;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "请求id")
    private String tokenId;

    @Schema(description = "限流令牌")
    private String soeLimit;
}
