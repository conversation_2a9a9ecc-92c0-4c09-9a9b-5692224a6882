package com.unipus.digitalbook.model.entity.qrcode;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;

/**
 * <p>
 * 二维码关系模板
 * </p>
 * new XSSFColor(new java.awt.Color(155,194,230), new DefaultIndexedColorMap()).getIndex()
 * IndexedColors.RED.getIndex()
 *
 * <AUTHOR>
 * @date 2025/2/24 17:01
 */
@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 44)
@HeadFontStyle(fontHeightInPoints = 11)
public class QrCodeRelationalTemplate {

    @ExcelProperty("二维码编号")
    private Long id;

    @ExcelProperty("教材中文名称")
    private String bookName;

    @ExcelProperty("实体教材位置")
    private String realBookLocation;

    @ExcelProperty("教材内部链接")
    private String bookInnerUrl;

    @ExcelProperty("二维码短链")
    private String qrCodeUrl;

    @ExcelProperty("备注")
    private String remarks;

    public QrCodeRelationalTemplate() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBookName() {
        return bookName;
    }

    public void setBookName(String bookName) {
        this.bookName = bookName;
    }

    public String getRealBookLocation() {
        return realBookLocation;
    }

    public void setRealBookLocation(String realBookLocation) {
        this.realBookLocation = realBookLocation;
    }

    public String getBookInnerUrl() {
        return bookInnerUrl;
    }

    public void setBookInnerUrl(String bookInnerUrl) {
        this.bookInnerUrl = bookInnerUrl;
    }

    public String getQrCodeUrl() {
        return qrCodeUrl;
    }

    public void setQrCodeUrl(String qrCodeUrl) {
        this.qrCodeUrl = qrCodeUrl;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
}
