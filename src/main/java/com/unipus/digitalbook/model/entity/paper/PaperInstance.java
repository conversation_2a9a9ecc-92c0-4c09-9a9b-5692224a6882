package com.unipus.digitalbook.model.entity.paper;

import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.enums.PaperPreviewModeEnum;
import com.unipus.digitalbook.model.enums.UnitTestModeEnum;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

@Schema(description = "试卷实例实体类")
public class PaperInstance {
    @Schema(description = "openId")
    private String openId;
    @Schema(description = "租户ID")
    private Long tenantId;
    @Schema(description = "成绩提交批次ID（主键ID）")
    private String scoreBatchId;

    @Schema(description = "试卷实例ID/作答轮次ID")
    private String instanceId;
    @Schema(description = "试卷ID")
    private String paperId;
    @Schema(description = "试卷版本")
    private String versionNumber;
    @Schema(description = "试卷名称")
    private String paperName;
    @Schema(description = "试卷类型")
    private PaperTypeEnum paperType;
    @Schema(description = "试卷说明")
    private String description;
    @Schema(description = "题目数量")
    private Integer questionCount;
    @Schema(description = "试卷总分")
    private BigDecimal totalScore;
    @Schema(description = "试卷内容")
    private String content;
    @Schema(description = "是否启用")
    private Boolean enable;
    @Schema(description = "试卷题目组列表")
    private List<BigQuestionGroup> bigQuestionGroupList;
    @Schema(description = "预览模式 1:教师/2:学生")
    private PaperPreviewModeEnum previewMode;
    @Schema(description = "是否清空预览历史数据，true:是/false:否")
    private Boolean clearPreviewHistory;
    @Schema(description = "诊断试卷测试模式 1:诊断/2:推荐")
    private UnitTestModeEnum testMode;
    @Schema(description = "诊断卷关联实例ID(诊断模式时：推荐卷实例ID，推荐模式时：诊断卷实例ID)")
    private String relatedInstanceId;

    public PaperInstance(){}

    public PaperInstance(String openId, Long tenantId, String scoreBatchId, String instanceId, Paper paper,
                         UnitTestModeEnum testMode, List<BigQuestionGroup> questionGroups){
        this.openId = openId;
        this.tenantId = tenantId;
        // 成绩提交批次ID
        this.scoreBatchId = scoreBatchId;
        // 试卷实例ID
        this.instanceId = instanceId;
        // 试卷ID
        this.paperId = paper.getPaperId();
        // 试卷版本
        this.versionNumber = paper.getVersionNumber();
        // 试卷名称
        this.paperName = paper.getPaperName();
        // 试卷类型（转换题组类型为试卷类型）
        this.paperType = paper.getPaperType();
        // 试卷说明
        this.description = paper.getDescription();
        // 题目数量
        this.questionCount = questionGroups.size();
        // 总分
        this.totalScore = questionGroups.stream().map(BigQuestionGroup::getScore)
                .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 试卷内容
        this.content = paper.getContent();
        // 试卷题组列表
        this.bigQuestionGroupList = questionGroups;
        this.enable = paper.getEnable();
        // 诊断试卷测试模式
        this.testMode = testMode;
    }

    // 取得试卷小题列表(试卷内所有大题下面的小题，平铺列表输出)
    public List<Question> getSmallQuestions(){
        if(CollectionUtils.isEmpty(this.bigQuestionGroupList)){
            return List.of();
        }
        return this.bigQuestionGroupList.stream().map(BigQuestionGroup::getQuestions).flatMap(List::stream).toList();
    }

    // 合计所有大题下面的小题数量
    public Integer getSmallQuestionTotalCountInPaper(){
        return this.getSmallQuestions().size();
    }

    // 取得排序后的题库列表(按照大题和小题的sortOrder排序)
    public List<BigQuestionGroup> getSortedQuestionGroups(){
        if(CollectionUtils.isEmpty(this.bigQuestionGroupList)){
            return List.of();
        }

        // 对大题进行排序
        List<BigQuestionGroup> groups = this.bigQuestionGroupList.stream()
                .sorted(Comparator.comparingInt(BigQuestionGroup::getSortOrder))
                .toList();
        
        // 对小题进行排序
        groups.forEach(group -> {
            if (!CollectionUtils.isEmpty(group.getQuestions())) {
                group.getQuestions().sort(Comparator.comparingInt(Question::getSortOrder));
            }
        });
        
        return groups;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getScoreBatchId() {
        return scoreBatchId;
    }

    public void setScoreBatchId(String scoreBatchId) {
        this.scoreBatchId = scoreBatchId;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public String getPaperName() {
        return paperName;
    }

    public void setPaperName(String paperName) {
        this.paperName = paperName;
    }

    public PaperTypeEnum getPaperType() {
        return paperType;
    }

    public void setPaperType(PaperTypeEnum paperType) {
        this.paperType = paperType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getQuestionCount() {
        return questionCount;
    }

    public void setQuestionCount(Integer questionCount) {
        this.questionCount = questionCount;
    }

    public BigDecimal getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(BigDecimal totalScore) {
        this.totalScore = totalScore;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public List<BigQuestionGroup> getBigQuestionGroupList() {
        return bigQuestionGroupList;
    }

    public void setBigQuestionGroupList(List<BigQuestionGroup> bigQuestionGroupList) {
        this.bigQuestionGroupList = bigQuestionGroupList;
    }

    public PaperPreviewModeEnum getPreviewMode() {
        return previewMode;
    }

    public void setPreviewMode(PaperPreviewModeEnum previewMode) {
        this.previewMode = previewMode;
    }

    public Boolean getClearPreviewHistory() {
        return clearPreviewHistory;
    }

    public void setClearPreviewHistory(Boolean clearPreviewHistory) {
        this.clearPreviewHistory = clearPreviewHistory;
    }

    public UnitTestModeEnum getTestMode() {
        return testMode;
    }

    public void setTestMode(UnitTestModeEnum testMode) {
        this.testMode = testMode;
    }

    public String getRelatedInstanceId() {
        return relatedInstanceId;
    }

    public void setRelatedInstanceId(String relatedInstanceId) {
        this.relatedInstanceId = relatedInstanceId;
    }
}
