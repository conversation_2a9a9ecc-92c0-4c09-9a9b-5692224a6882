package com.unipus.digitalbook.model.entity;

/**
 * 组织角色信息
 */
public class OrgInfo {

    /**
     * 组织ID
     */
    private Long id;

    /**
     * 组织名称
     */
    private String orgName;

    /**
     * 组织类型
     */
    private Integer orgType;

    /**
     * 父组织ID
     */
    private Long parentId;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Integer getOrgType() {
        return orgType;
    }

    public void setOrgType(Integer orgType) {
        this.orgType = orgType;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public static OrgInfo build(Organization organization) {
        OrgInfo orgInfo = new OrgInfo();
        orgInfo.setId(organization.getId());
        orgInfo.setOrgName(organization.getOrgName());
        orgInfo.setOrgType(organization.getOrgType());
        orgInfo.setParentId(organization.getParentId());
        return orgInfo;
    }
}
