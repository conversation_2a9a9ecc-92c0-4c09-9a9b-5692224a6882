package com.unipus.digitalbook.model.entity.question.type;

import com.unipus.digitalbook.model.entity.question.IQuestion;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.UserAnswer;

import java.math.BigDecimal;

/**
 * 量表题
 */
public class EvaluationQuestion extends Question implements IQuestion {

    @Override
    public BigDecimal doScore(double accuracy) {
        return null;
    }

    @Override
    public double judge(UserAnswer userAnswer) {
        return 0;
    }
}
