package com.unipus.digitalbook.model.entity.question;

import com.unipus.digitalbook.common.utils.ApplicationContextUtil;
import com.unipus.digitalbook.model.enums.QuestionTypeEnum;
import com.unipus.digitalbook.model.enums.UserAnswerStatusEnum;
import com.unipus.digitalbook.service.judge.JudgeStrategy;
import com.unipus.digitalbook.service.judge.JudgeStrategyFactory;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户答案列表
 */
public class UserAnswerList {
    /**
     * 用户答案列表
     */
    private List<UserAnswer> userAnswers;

    /**
     * 总分
     */
    private BigDecimal score;

    public UserAnswerList() {

    }

    public UserAnswerList(List<UserAnswer> userAnswers) {
        this.userAnswers = userAnswers;
    }

    public UserAnswerList score(List<Question> questions) {
        if (CollectionUtils.isEmpty(questions)) {
            return this;
        }
        Map<String, UserAnswer> questionAnswerMap = getUserAnswers().stream().collect(Collectors.toMap(UserAnswer::getBizQuestionId, v -> v, (v1, v2) -> v1));
        BigDecimal score = questions.stream().map(question -> {
            return score(question, questionAnswerMap);
        }).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        this.setScore(score);
        return this;
    }

    private BigDecimal score(Question question, Map<String, UserAnswer> questionAnswerMap) {
        if (question instanceof IQuestion) {
            UserAnswer userAnswer = questionAnswerMap.get(question.getBizQuestionId());
            if (userAnswer == null) {
                return BigDecimal.ZERO;
            }
            userAnswer.setStatus(UserAnswerStatusEnum.UN_JUDGMENT.getCode());
            if (question.getIsJudgment() == null || !question.getIsJudgment()) {
                return BigDecimal.ZERO;
            }
            double accuracy = question.judge(userAnswer);
            if (accuracy == 0) {
                userAnswer.setStatus(UserAnswerStatusEnum.ERROR.getCode());
            } else if (accuracy == 1) {
                userAnswer.setStatus(UserAnswerStatusEnum.CORRECT.getCode());
            } else {
                userAnswer.setStatus(UserAnswerStatusEnum.HALF_CORRECT.getCode());
            }
            if (question.getIsScoring() == null || !question.getIsScoring()) {
                return BigDecimal.ZERO;
            }
            BigDecimal userScore = question.doScore(accuracy);
            userAnswer.setScore(userScore);
            return userScore;
        }
        if (CollectionUtils.isEmpty(question.getQuestions())) {
            return BigDecimal.ZERO;
        }
        return question.getQuestions().stream().map(q -> {
            return score(q, questionAnswerMap);
        }).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }

    public List<JudgeTaskTicket> startJudgeTask(List<Question> questions) {
        if (CollectionUtils.isEmpty(questions)) {
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(getUserAnswers())) {
            return Collections.emptyList();
        }
        Map<String, UserAnswer> questionAnswerMap = getUserAnswers().stream().collect(Collectors.toMap(UserAnswer::getBizQuestionId, v -> v, (v1, v2) -> v1));
        List<JudgeTaskTicket> judgeTaskTickets = new ArrayList<>();
        questions.forEach(question -> {
            startJudgeTask(question, questionAnswerMap, judgeTaskTickets);
        });
        return judgeTaskTickets;
    }

    private void startJudgeTask(Question question, Map<String, UserAnswer> questionAnswerMap, List<JudgeTaskTicket> judgeTaskTickets) {
        if (question instanceof IQuestion) {
            UserAnswer userAnswer = questionAnswerMap.get(question.getBizQuestionId());
            judgeTaskTickets.add(question.getJudgeStrategy().startJudgeTask(question, userAnswer));
            return;
        }
        question.getQuestions().forEach(q -> {
            startJudgeTask(q, questionAnswerMap, judgeTaskTickets);
        });
    }

    public List<UserAnswer> fetchJudgeResult() {
        if (userAnswers == null || userAnswers.isEmpty()) {
            return userAnswers;
        }
        List<UserAnswer> result = new ArrayList<>();
        userAnswers.stream().collect(Collectors.groupingBy(UserAnswer::getQuestionType)).forEach((questionType, userAnswers) -> {
            JudgeStrategy<Question> strategy = ApplicationContextUtil.getBean(JudgeStrategyFactory.class)
                    .getStrategy(QuestionTypeEnum.getEnumByCode(questionType));
            userAnswers.forEach(userAnswer -> {
                result.add(strategy.fetchJudgeResult(userAnswer));
            });
        });
        return result;
    }

    public void endJudgeTask() {
        if (userAnswers == null || userAnswers.isEmpty()) {
            return;
        }
        userAnswers.stream().collect(Collectors.groupingBy(UserAnswer::getQuestionType)).forEach((questionType, userAnswers) -> {
            JudgeStrategy<Question> strategy = ApplicationContextUtil.getBean(JudgeStrategyFactory.class)
                    .getStrategy(QuestionTypeEnum.getEnumByCode(questionType));
            userAnswers.forEach(strategy::endJudgeTask);
        });
    }

    public List<UserAnswer> getUserAnswers() {
        return userAnswers;
    }

    public void setUserAnswers(List<UserAnswer> userAnswers) {
        this.userAnswers = userAnswers;
    }

    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }
}
