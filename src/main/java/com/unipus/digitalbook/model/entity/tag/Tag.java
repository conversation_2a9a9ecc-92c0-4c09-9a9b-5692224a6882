package com.unipus.digitalbook.model.entity.tag;

import java.util.List;

/**
 * 标签
 */
public class Tag {
    /**
     * 标签ID
     */
    private Long tagId;
    /**
     * 父标签ID
     */
    private Long parentId;
    /**
     * 标签类型:1:技能点
     */
    private Integer tagType;
    /**
     * 标签名称
     */
    private String tagName;
    /**
     * 关联资源ID
     */
    private String resourceId;
    /**
     * 标签层级
     */
    private Integer level;
    /**
     * 下级标签列表
     */
    private List<Tag> children;
    /**
     * 全路径ID列表
     */
    private List<Long> fullPathIdList;

    // 实现Tag对象复制（数据来源数据库，平铺模式）
    public Tag build(String resourceId){
        Tag newTag = new Tag();
        newTag.setTagId(this.getTagId());
        newTag.setParentId(this.getParentId());
        newTag.setTagType(this.getTagType());
        newTag.setTagName(this.getTagName());
        newTag.setResourceId(resourceId);
        newTag.setLevel(this.getLevel());
        return newTag;
    }

    public Long getTagId() {
        return tagId;
    }

    public void setTagId(Long tagId) {
        this.tagId = tagId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getTagType() {
        return tagType;
    }

    public void setTagType(Integer tagType) {
        this.tagType = tagType;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public List<Tag> getChildren() {
        return children;
    }

    public void setChildren(List<Tag> children) {
        this.children = children;
    }

    public List<Long> getFullPathIdList() {
        return fullPathIdList;
    }

    public void setFullPathIdList(List<Long> fullPathIdList) {
        this.fullPathIdList = fullPathIdList;
    }
}
