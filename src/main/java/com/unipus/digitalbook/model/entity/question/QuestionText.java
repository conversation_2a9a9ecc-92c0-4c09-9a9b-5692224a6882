package com.unipus.digitalbook.model.entity.question;

import com.unipus.digitalbook.common.utils.ArrayUtil;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.enums.QuestionRelationTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * 题干
 */
public class QuestionText {
    /**
     * 题干
     */
    private String text;
    /**
     * 文本题干
     */
    private String plainText;

    /**
     * 题干音频
     */
    private String media;

    /**
     * 角色
     */
    private String role;

    /**
     * 音标
     */
    private String phoneticSymbol;

    /**
     * 关键词
     */
    private List<Keyword> keywords;

    /**
     * 关联
     */
    private List<Relevancy> relevancy;

    /**
     * 选项
     */
    private List<ChoiceQuestionOption> options;

    /**
     * 答案字数限制
     */
    private Integer answerWordLimit;

    /**
     * 准备时长
     */
    private Integer prepareTime;

    /**
     * 作答间隔时长
     */
    private Integer answerTime;

    /**
     * 翻译文本
     */
    private String evaluationText;

    public QuestionText() {}
    public QuestionText(String text, String plainText) {
        this.text = text;
        this.plainText = plainText;
    }
    public String serialize() {
        if (StringUtils.hasText(text) ||
                StringUtils.hasText(plainText) ||
                StringUtils.hasText(media) ||
                StringUtils.hasText(role) ||
                StringUtils.hasText(phoneticSymbol) ||
                StringUtils.hasText(evaluationText) ||
                answerWordLimit != null ||
                prepareTime != null ||
                answerTime != null ||
                CollectionUtils.isNotEmpty(keywords) ||
                CollectionUtils.isNotEmpty(relevancy) ||
                CollectionUtils.isNotEmpty(options)) {
            return JsonUtil.toJsonString(this);
        }
        return null;
    }
    public static QuestionText deserialize(String questionText) {
        if (StringUtils.hasText(questionText)) {
            return JsonUtil.parseObject(questionText, QuestionText.class);
        }
        return new QuestionText();
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getPlainText() {
        return plainText;
    }

    public void setPlainText(String plainText) {
        this.plainText = plainText;
    }

    public String getMedia() {
        return media;
    }

    public void setMedia(String media) {
        this.media = media;
    }

    public List<Keyword> getKeywords() {
        return keywords;
    }

    public void setKeywords(List<Keyword> keywords) {
        this.keywords = keywords;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getPhoneticSymbol() {
        return phoneticSymbol;
    }

    public void setPhoneticSymbol(String phoneticSymbol) {
        this.phoneticSymbol = phoneticSymbol;
    }

    public List<Relevancy> getRelevancy() {
        return relevancy;
    }

    public void setRelevancy(List<Relevancy> relevancy) {
        this.relevancy = relevancy;
    }

    public List<ChoiceQuestionOption> getOptions() {
        return options;
    }

    public void setOptions(List<ChoiceQuestionOption> options) {
        this.options = options;
    }

    public Integer getAnswerWordLimit() {
        return answerWordLimit;
    }

    public void setAnswerWordLimit(Integer answerWordLimit) {
        this.answerWordLimit = answerWordLimit;
    }

    public Integer getPrepareTime() {
        return prepareTime;
    }

    public void setPrepareTime(Integer prepareTime) {
        this.prepareTime = prepareTime;
    }

    public Integer getAnswerTime() {
        return answerTime;
    }

    public void setAnswerTime(Integer answerTime) {
        this.answerTime = answerTime;
    }

    public String getEvaluationText() {
        return evaluationText;
    }

    public void setEvaluationText(String evaluationText) {
        this.evaluationText = evaluationText;
    }

    public static class Keyword  {
        private String text;
        private String id;

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            Keyword that = (Keyword) o;
            return Objects.equals(text, that.text) &&
                    Objects.equals(id, that.id);
        }
        @Override
        public int hashCode() {
            return Objects.hash(text, id);
        }
    }

    public static class Relevancy {
        private String id;
        private String type;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            Relevancy that = (Relevancy) o;
            return Objects.equals(id, that.id) && Objects.equals(type, that.type);
        }
        @Override
        public int hashCode() {
            return Objects.hash(id , type);
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        QuestionText that = (QuestionText) o;
        return Objects.equals(text, that.text) &&
                Objects.equals(plainText, that.plainText) &&
                Objects.equals(media, that.media) &&
                Objects.equals(role, that.role) &&
                Objects.equals(phoneticSymbol, that.phoneticSymbol) &&
                Objects.equals(evaluationText, that.evaluationText) &&
                Objects.equals(answerWordLimit, that.answerWordLimit) &&
                Objects.equals(prepareTime, that.prepareTime) &&
                Objects.equals(answerTime, that.answerTime) &&
                ArrayUtil.equalsIgnoreNullAndOrder(relevancy, that.relevancy) &&
                ArrayUtil.equalsIgnoreNullAndOrder(options, that.options) &&
                ArrayUtil.equalsIgnoreNullAndOrder(keywords, that.keywords);
    }
    @Override
    public int hashCode() {
        return Objects.hash(text, plainText, media, phoneticSymbol, role, answerWordLimit, prepareTime, answerTime, evaluationText);
    }

    public Boolean hasTargetRelation(QuestionRelationTypeEnum typeEnum) {
        if(CollectionUtils.isEmpty(this.relevancy)){
            return false;
        }
        return relevancy.stream().anyMatch(relevancy -> typeEnum.match(relevancy.getType()));
    }

    public List<String> getTargetRelationIds(QuestionRelationTypeEnum typeEnum) {
        if(CollectionUtils.isEmpty(this.relevancy)){
            return null;
        }
        return relevancy.stream().filter(relevancy -> typeEnum.match(relevancy.getType())).map(Relevancy::getId).toList();
    }
}
