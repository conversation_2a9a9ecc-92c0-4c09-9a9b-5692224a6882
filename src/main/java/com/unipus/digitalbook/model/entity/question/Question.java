package com.unipus.digitalbook.model.entity.question;

import com.unipus.digitalbook.common.utils.ApplicationContextUtil;
import com.unipus.digitalbook.common.utils.ArrayUtil;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.enums.QuestionTypeEnum;
import com.unipus.digitalbook.service.judge.JudgeStrategy;
import com.unipus.digitalbook.service.judge.JudgeStrategyFactory;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 题目
 */
public abstract class Question {

    private Long id;
    /**
     * 题目ID
     */
    private String bizQuestionId;

    /**
     * 题目版本号
     */
    private String versionNumber;

    /**
     * 题目类型 单选、多选
     * @see com.unipus.digitalbook.model.enums.QuestionTypeEnum
     */
    private Integer questionType;

    /**
     * 题干
     */
    private QuestionText questionText;

    /**
     * 作答提示
     */
    private String direction;

    /**
     * 材料内容
     */
    private String content;

    /**
     * 题组难度级别0-5
     */
    private Integer difficulty;

    /**
     * 是否记分
     */
    private Boolean isScoring;

    /**
     * 是否自动判题
     */
    private Boolean isJudgment;

    /**
     * 答案解析
     */
    private String analysis;
    /**
     * 分数
     */
    private BigDecimal score;

    /**
     * 题目正确答案列表
     */
    private List<QuestionAnswer> answers;

    /**
     * 题目选项列表
     */
    private List<ChoiceQuestionOption> options;
    /**
     * 题目顺序
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

    /**
     * 标签列表
     */
    private List<List<QuestionTag>> tags;
    /**
     * 题目
     */
    private List<Question> questions;


    /**
     * 计算分数
     *
     * @param accuracy 准确率
     * @return 分数
     */
    public abstract BigDecimal doScore(double accuracy);

    /**
     * 判断
     *
     * @param userAnswer 用户答案列表
     * @return 得分百分比
     */
    public abstract double judge(UserAnswer userAnswer);
    /**
     * 计算分数
     *
     * @param userAnswer 用户答案列表
     * @return 分数
     */
    public BigDecimal score(UserAnswer userAnswer) {
        double accuracy = judge(userAnswer);
        return doScore(accuracy);
    }

    public <T extends Question> JudgeStrategy<T> getJudgeStrategy() {
        return ApplicationContextUtil.getBean(JudgeStrategyFactory.class).getStrategy(QuestionTypeEnum.getEnumByCode(this.getQuestionType()));
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getAnalysis() {
        return analysis;
    }

    public void setAnalysis(String analysis) {
        this.analysis = analysis;
    }

    public Integer getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(Integer difficulty) {
        this.difficulty = difficulty;
    }

    public Boolean getIsScoring() {
        return isScoring;
    }

    public void setIsScoring(Boolean scoring) {
        isScoring = scoring;
    }

    public Boolean getIsJudgment() {
        return isJudgment;
    }

    public void setIsJudgment(Boolean judgment) {
        isJudgment = judgment;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBizQuestionId() {
        return bizQuestionId;
    }

    public void setBizQuestionId(String bizQuestionId) {
        this.bizQuestionId = bizQuestionId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Integer getQuestionType() {
        return questionType;
    }

    public void setQuestionType(Integer questionType) {
        this.questionType = questionType;
    }

    public QuestionText getQuestionText() {
        return questionText;
    }

    public void setQuestionText(QuestionText questionText) {
        this.questionText = questionText;
    }

    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public List<QuestionAnswer> getAnswers() {
        return answers;
    }

    public void setAnswers(List<QuestionAnswer> answers) {
        this.answers = answers;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public List<Question> getQuestions() {
        return questions;
    }

    public void setQuestions(List<Question> questions) {
        this.questions = questions;
    }

    public List<ChoiceQuestionOption> getOptions() {
        return options;
    }

    public void setOptions(List<ChoiceQuestionOption> options) {
        this.options = options;
    }

    public List<List<QuestionTag>> getTags() {
        return tags;
    }

    public void setTags(List<List<QuestionTag>> tags) {
        this.tags = tags;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Question that = (Question) o;
        return isBigDecimalEqual(score, that.score) &&
                Objects.equals(bizQuestionId, that.bizQuestionId) &&
                Objects.equals(questionType, that.questionType) &&
                Objects.equals(direction, that.direction) &&
                Objects.equals(content, that.content) &&
                Objects.equals(isScoring, that.isScoring) &&
                Objects.equals(isJudgment, that.isJudgment) &&
                Objects.equals(questionText, that.questionText) &&
                Objects.equals(difficulty, that.difficulty) &&
                Objects.equals(analysis, that.analysis) &&
                Objects.equals(sortOrder, that.sortOrder) &&
                ArrayUtil.equalsIgnoreNullAndOrder(questions, that.questions) &&
                ArrayUtil.equalsIgnoreNullAndOrder(options, that.options) &&
                ArrayUtil.equalsIgnoreNullAndOrder(answers, that.answers) &&
                ArrayUtil.equalsIgnoreNullAndOrder(tags, that.tags);
    }
    public static boolean isBigDecimalEqual(BigDecimal a, BigDecimal b) {
        return (a == null && b == null) || (a != null && b != null && a.compareTo(b) == 0);
    }

    @Override
    public int hashCode() {
        return Objects.hash(bizQuestionId, direction, content, isScoring, isJudgment, questionText, difficulty, analysis, sortOrder);
    }
    @Override
    public String toString() {
        return "Question{" +
                "id=" + id +
                ", bizQuestionId='" + bizQuestionId + '\'' +
                ", versionNumber='" + versionNumber + '\'' +
                ", questionType=" + questionType +
                ", questionText='" + questionText + '\'' +
                ", direction='" + direction + '\'' +
                ", content='" + content + '\'' +
                ", difficulty=" + difficulty +
                ", isScoring=" + isScoring +
                ", isJudgment=" + isJudgment +
                ", analysis='" + analysis + '\'' +
                ", score=" + score +
                ", answers=" + answers +
                ", options=" + options +
                ", sortOrder=" + sortOrder +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createBy=" + createBy +
                ", updateBy=" + updateBy +
                ", enable=" + enable +
                ", questions=" + questions +
                '}';
    }
}