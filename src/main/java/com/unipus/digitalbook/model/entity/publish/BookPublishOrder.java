package com.unipus.digitalbook.model.entity.publish;

import com.unipus.digitalbook.model.entity.book.BookNode;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class BookPublishOrder {
    /**
     * 教材id
     */
    private String bookId;
    /**
     * 教材发布资源列表
     */
    private List<BookNode> publishResourceList;

    private Map<String, List<BookNode>> classListMap;


    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
        if (publishResourceList != null) {
            publishResourceList.forEach(bookNode -> bookNode.setBookId(bookId));
        }
    }

    public List<BookNode> getPublishResourceList() {
        return publishResourceList;
    }

    public void setPublishResourceList(List<BookNode> publishResourceList) {
        this.publishResourceList = publishResourceList;
        if (bookId != null) {
            publishResourceList.forEach(bookNode -> bookNode.setBookId(bookId));
        }
        classListMap = publishResourceList.stream()
                .collect(Collectors.groupingBy(bookNode -> bookNode.getClass().getSimpleName()));
    }


}
