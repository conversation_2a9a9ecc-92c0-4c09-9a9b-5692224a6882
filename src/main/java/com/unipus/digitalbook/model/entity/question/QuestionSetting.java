package com.unipus.digitalbook.model.entity.question;

import java.util.Objects;

/**
 * 题组设置
 */
public class QuestionSetting {
    /**
     * 配置id
     */
    private Long id;
    /**
     * 题组id
     */
    private Long groupId;
    /**
     * 答题方式，如拖拽、点选
     */
    private String answerType;

    /**
     * PC 端布局类型，如瀑布、分页、左右
     */
    private String pcLayoutType;

    /**
     * APP 端布局类型，如瀑布、分页
     */
    private String appLayoutType;

    /**
     * 音频设置信息
     */
    private AudioSetting audioSetting;

    /**
     * 视频设置信息
     */
    private VideoSetting videoSetting;

    /**
     * 作答角色 'all' | 'role' | null
     */
    private String answerRole;

    /**
     * 听原音 可选 'before' | 'after'| null
     */
    private String answerTiming;

    /**
     * 作答量级
     */
    private String answerLevel;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        QuestionSetting that = (QuestionSetting) o;
        return Objects.equals(answerType, that.answerType) &&
                Objects.equals(pcLayoutType, that.pcLayoutType) &&
                Objects.equals(appLayoutType, that.appLayoutType) &&
                Objects.equals(answerRole, that.answerRole) &&
                Objects.equals(answerTiming, that.answerTiming) &&
                Objects.equals(answerLevel, that.answerLevel) &&
                Objects.equals(audioSetting, that.audioSetting) &&
                Objects.equals(videoSetting, that.videoSetting);
    }
    @Override
    public int hashCode() {
        return Objects.hash(answerType, pcLayoutType, appLayoutType, answerRole, answerTiming, answerLevel, audioSetting, videoSetting);
    }
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAnswerType() {
        return answerType;
    }

    public void setAnswerType(String answerType) {
        this.answerType = answerType;
    }

    public String getPcLayoutType() {
        return pcLayoutType;
    }

    public void setPcLayoutType(String pcLayoutType) {
        this.pcLayoutType = pcLayoutType;
    }

    public String getAppLayoutType() {
        return appLayoutType;
    }

    public void setAppLayoutType(String appLayoutType) {
        this.appLayoutType = appLayoutType;
    }

    public AudioSetting getAudioSetting() {
        return audioSetting;
    }

    public void setAudioSetting(AudioSetting audioSetting) {
        this.audioSetting = audioSetting;
    }

    public VideoSetting getVideoSetting() {
        return videoSetting;
    }

    public void setVideoSetting(VideoSetting videoSetting) {
        this.videoSetting = videoSetting;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }


    public String getAnswerRole() {
        return answerRole;
    }

    public void setAnswerRole(String answerRole) {
        this.answerRole = answerRole;
    }

    public String getAnswerTiming() {
        return answerTiming;
    }

    public void setAnswerTiming(String answerTiming) {
        this.answerTiming = answerTiming;
    }

    public String getAnswerLevel() {
        return answerLevel;
    }

    public void setAnswerLevel(String answerLevel) {
        this.answerLevel = answerLevel;
    }

    /**
     * 视频设置
     */
    public static class VideoSetting {
        /**
         * 播放方式，如点击播放、自动播放
         */
        private String playType;
        /**
         * 作答前显示，作答后显示
         */
        private String subtitle;

        /**
         * 是否设置视频播放次数
         */
        private boolean setPlayNum;
        /**
         * 视频设置播放次数的值
         */
        private Integer playNum;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            VideoSetting that = (VideoSetting) o;
            return Objects.equals(playType, that.playType) &&
                    Objects.equals(subtitle, that.subtitle) &&
                    Objects.equals(setPlayNum, that.setPlayNum) &&
                    Objects.equals(playNum, that.playNum);
        }
        @Override
        public int hashCode() {
            return Objects.hash(playType, subtitle, setPlayNum, playNum);
        }
        public String getPlayType() {
            return playType;
        }

        public void setPlayType(String playType) {
            this.playType = playType;
        }

        public String getSubtitle() {
            return subtitle;
        }

        public void setSubtitle(String subtitle) {
            this.subtitle = subtitle;
        }

        public boolean isSetPlayNum() {
            return setPlayNum;
        }

        public void setSetPlayNum(boolean setPlayNum) {
            this.setPlayNum = setPlayNum;
        }

        public Integer getPlayNum() {
            return playNum;
        }

        public void setPlayNum(Integer playNum) {
            this.playNum = playNum;
        }
    }

    /**
     * 音频设置
     */
    public static class AudioSetting {
        /**
         * 播放方式，如点击播放、自动播放
         */
        private String playType;
        /**
         * 作答前显示，作答后显示
         */
        private String subtitle;

        /**
         * 是否设置音频播放次数
         */
        private boolean setPlayNum;
        /**
         * 视频设置音频次数的值
         */
        private Integer playNum;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            AudioSetting that = (AudioSetting) o;
            return Objects.equals(playType, that.playType) &&
                    Objects.equals(subtitle, that.subtitle) &&
                    Objects.equals(setPlayNum, that.setPlayNum) &&
                    Objects.equals(playNum, that.playNum);
        }
        @Override
        public int hashCode() {
            return Objects.hash(playType, subtitle, setPlayNum, playNum);
        }
        public String getPlayType() {
            return playType;
        }

        public void setPlayType(String playType) {
            this.playType = playType;
        }

        public String getSubtitle() {
            return subtitle;
        }

        public void setSubtitle(String subtitle) {
            this.subtitle = subtitle;
        }

        public boolean isSetPlayNum() {
            return setPlayNum;
        }

        public void setSetPlayNum(boolean setPlayNum) {
            this.setPlayNum = setPlayNum;
        }

        public Integer getPlayNum() {
            return playNum;
        }

        public void setPlayNum(Integer playNum) {
            this.playNum = playNum;
        }
    }
}
