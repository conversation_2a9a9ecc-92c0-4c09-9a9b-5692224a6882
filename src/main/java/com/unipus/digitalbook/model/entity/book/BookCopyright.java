package com.unipus.digitalbook.model.entity.book;

import com.unipus.digitalbook.common.utils.IdentifierUtil;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 教材版权信息
 */
public class BookCopyright extends BookNode implements BookInfoInterface{
    private Long id;

    /**
     * 版本号
     */
    private String versionNumber;

    /**
     * 总主编
     */
    private String chiefEditor;

    /**
     * 主编
     */
    private String editor;

    /**
     * 副主编
     */
    private String deputyEditor;

    /**
     * 项目策划
     */
    private String projectPlanner;

    /**
     * 责任编辑
     */
    private String executiveEditor;

    /**
     * 责任校对
     */
    private String proofreader;

    /**
     * 数字编辑
     */
    private String digitalEditor;

    /**
     * 封面设计
     */
    private String coverDesigner;

    /**
     * 版式设计
     */
    private String layoutDesigner;

    /**
     * 出版发行
     */
    private String publisher;

    /**
     * 字数
     */
    private String wordCount;

    /**
     * 出版时间-年
     */
    private Integer publishYear;

    /**
     * 出版时间-月
     */
    private Integer publishMonth;

    /**
     * 出版时间-日
     */
    private Integer publishDay;

    /**
     * ISBN
     */
    private String isbn;

    /**
     * 定价
     */
    private BigDecimal price;

    /**
     * 版次
     */
    private String edition;


    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

    /**
     * HashCode
     */
    private String hashCode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getChiefEditor() {
        return chiefEditor;
    }

    public void setChiefEditor(String chiefEditor) {
        this.chiefEditor = chiefEditor;
    }

    public String getEditor() {
        return editor;
    }

    public void setEditor(String editor) {
        this.editor = editor;
    }

    public String getDeputyEditor() {
        return deputyEditor;
    }

    public void setDeputyEditor(String deputyEditor) {
        this.deputyEditor = deputyEditor;
    }

    public String getProjectPlanner() {
        return projectPlanner;
    }

    public void setProjectPlanner(String projectPlanner) {
        this.projectPlanner = projectPlanner;
    }

    public String getExecutiveEditor() {
        return executiveEditor;
    }

    public void setExecutiveEditor(String executiveEditor) {
        this.executiveEditor = executiveEditor;
    }

    public String getProofreader() {
        return proofreader;
    }

    public void setProofreader(String proofreader) {
        this.proofreader = proofreader;
    }

    public String getDigitalEditor() {
        return digitalEditor;
    }

    public void setDigitalEditor(String digitalEditor) {
        this.digitalEditor = digitalEditor;
    }

    public String getCoverDesigner() {
        return coverDesigner;
    }

    public void setCoverDesigner(String coverDesigner) {
        this.coverDesigner = coverDesigner;
    }

    public String getLayoutDesigner() {
        return layoutDesigner;
    }

    public void setLayoutDesigner(String layoutDesigner) {
        this.layoutDesigner = layoutDesigner;
    }

    public String getPublisher() {
        return publisher;
    }

    public void setPublisher(String publisher) {
        this.publisher = publisher;
    }

    public String getWordCount() {
        return wordCount;
    }

    public void setWordCount(String wordCount) {
        this.wordCount = wordCount;
    }

    public Integer getPublishYear() {
        return publishYear;
    }

    public void setPublishYear(Integer publishYear) {
        this.publishYear = publishYear;
    }

    public Integer getPublishMonth() {
        return publishMonth;
    }

    public void setPublishMonth(Integer publishMonth) {
        this.publishMonth = publishMonth;
    }

    public Integer getPublishDay() {
        return publishDay;
    }

    public void setPublishDay(Integer publishDay) {
        this.publishDay = publishDay;
    }

    public String getIsbn() {
        return isbn;
    }

    public void setIsbn(String isbn) {
        this.isbn = isbn;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public String getEdition() {
        return edition;
    }

    public void setEdition(String edition) {
        this.edition = edition;
    }

    public String getHashCode() {
        return hashCode;
    }

    public void setHashCode(String hashCode) {
        this.hashCode = hashCode;
    }

    // 完成深拷贝
    public BookCopyright deepCopy() {
        BookCopyright copy = new BookCopyright();
        copy.setChiefEditor(this.getChiefEditor());
        copy.setEditor(this.getEditor());
        copy.setDeputyEditor(this.getDeputyEditor());
        copy.setProjectPlanner(this.getProjectPlanner());
        copy.setExecutiveEditor(this.getExecutiveEditor());
        copy.setProofreader(this.getProofreader());
        copy.setDigitalEditor(this.getDigitalEditor());
        copy.setCoverDesigner(this.getCoverDesigner());
        copy.setLayoutDesigner(this.getLayoutDesigner());
        copy.setPublisher(this.getPublisher());
        copy.setWordCount(this.getWordCount());
        copy.setPublishYear(this.getPublishYear());
        copy.setPublishMonth(this.getPublishMonth());
        copy.setPublishDay(this.getPublishDay());
        copy.setIsbn(this.getIsbn());
        copy.setPrice(this.getPrice());
        copy.setEdition(this.getEdition());
        copy.setBookId(this.getBookId());
        // 版本号使用当前时间戳进行编码
        copy.setVersionNumber(IdentifierUtil.generateVersion());
        copy.setEnable(this.getEnable());
        return copy;
    }

}
