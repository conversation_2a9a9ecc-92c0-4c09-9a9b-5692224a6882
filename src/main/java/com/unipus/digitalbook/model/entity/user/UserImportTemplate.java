package com.unipus.digitalbook.model.entity.user;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;

/**
 * <p>
 * 用户导入模板
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/3 17:01
 */
public class UserImportTemplate {

    /**
     * 用户昵称
     */
    @ExcelProperty("用户昵称")
    private String nickName;

    /**
     * 注册手机号
     */
    @ExcelProperty("注册手机号")
    private String cellPhone;

    /**
     * 用户角色名称
     */
    @ExcelProperty("用户角色")
    private String roleName;

    /**
     * 用户角色Code
     */
    @ExcelProperty("用户角色Code")
    private String roleCode;

    /**
     * 行号
     */
    @ExcelIgnore
    private Integer num;

    /**
     * 是否有效
     */
    @ExcelIgnore
    private boolean valid = true;

    /**
     * 错误信息
     */
    @ExcelIgnore
    private String errorMsg;

    public UserImportTemplate() {
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getCellPhone() {
        return cellPhone;
    }

    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public boolean isValid() {
        return valid;
    }

    public void setValid(boolean valid) {
        this.valid = valid;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}
