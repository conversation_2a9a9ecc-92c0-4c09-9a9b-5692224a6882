package com.unipus.digitalbook.model.entity.book;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 教材章节主题关系实体
 */
public class BookTheme implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;
    /**
     * 关联教材ID
     */
    private String bookId;
    /**
     * 生效章节ID，‘-’代表全部章节
     */
    private String chapterId;
    /**
     * 主题ID
     */
    private Long themeId;
    /**
     * 是否覆盖已编写内容的默认样式，false:否，true:是
     */
    private Boolean overrideDefault;
    /**
     * 是否覆盖已编写内容的自定义样式，false:否，true:是
     */
    private Boolean overrideCustom;
    /**
     * 是否作为生效编辑器的样式模板，false:否，true:是
     */
    private Boolean editorTemplate;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 是否有效
     */
    private Boolean enable;

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Boolean getEditorTemplate() {
        return editorTemplate;
    }

    public void setEditorTemplate(Boolean editorTemplate) {
        this.editorTemplate = editorTemplate;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Boolean getOverrideCustom() {
        return overrideCustom;
    }

    public void setOverrideCustom(Boolean overrideCustom) {
        this.overrideCustom = overrideCustom;
    }

    public Boolean getOverrideDefault() {
        return overrideDefault;
    }

    public void setOverrideDefault(Boolean overrideDefault) {
        this.overrideDefault = overrideDefault;
    }

    public Long getThemeId() {
        return themeId;
    }

    public void setThemeId(Long themeId) {
        this.themeId = themeId;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
