package com.unipus.digitalbook.model.entity;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 异步处理异常日志实体
 */
public class AsyncFailLog implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 操作：create update delete
     */
    private String op;
    /**
     * 对象类型 1:Org、2:User、3:OrgUserRelation、4:BOOK
     */
    private String type;
    /**
     * 对象
     */
    private String object;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 状态  0-重试后成功 1-首次错误 2-重试错误
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 重试失败时间
     */
    private Date retryFailTime;
    /**
     * 成功时间
     */
    private Date successTime;

    public static AsyncFailLog builder() {
        return new AsyncFailLog();
    }

    public Long getId() {
        return id;
    }

    public AsyncFailLog setId(Long id) {
        this.id = id;
        return this;
    }

    public String getOp() {
        return op;
    }

    public AsyncFailLog setOp(String op) {
        this.op = op;
        return this;
    }

    public String getType() {
        return type;
    }

    public AsyncFailLog setType(String type) {
        this.type = type;
        return this;
    }

    public String getObject() {
        return object;
    }

    public AsyncFailLog setObject(String object) {
        this.object = object;
        return this;
    }

    public String getFailReason() {
        return failReason;
    }

    public AsyncFailLog setFailReason(String failReason) {
        this.failReason = failReason;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public AsyncFailLog setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public AsyncFailLog setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getRetryFailTime() {
        return retryFailTime;
    }

    public AsyncFailLog setRetryFailTime(Date retryFailTime) {
        this.retryFailTime = retryFailTime;
        return this;
    }

    public Date getSuccessTime() {
        return successTime;
    }

    public AsyncFailLog setSuccessTime(Date successTime) {
        this.successTime = successTime;
        return this;
    }

    public AsyncFailLog build() {
        return this;
    }
}
