package com.unipus.digitalbook.model.entity.question;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.entity.paper.UserPaperSyncInfo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class UserAnswerData {

    private String answer;

    private String dataPackage;

    public UserAnswerData(){}

    public UserAnswerData(BigQuestionGroup question, List<UserAnswer> userAnswers, String dataPackage) {
        this.setDataPackage(dataPackage);
        this.setAnswer(JsonUtil.toJsonString(new UserAnswerNode().toUserAnswerNode(question, userAnswers)));
    }
    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public String getDataPackage() {
        return dataPackage;
    }

    public void setDataPackage(String dataPackage) {
        this.dataPackage = dataPackage;
    }
}
