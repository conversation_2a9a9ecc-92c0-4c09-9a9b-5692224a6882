package com.unipus.digitalbook.model.entity.user;

import java.util.List;

/**
 * 用户列表模型。
 */
public class SearchUserList {

    /**
     * 用户列表
     */
    private List<SearchUser> userList;

    /**
     * 总数量
     */
    private Integer total;

    public List<SearchUser> getUserList() {
        return userList;
    }

    public void setUserList(List<SearchUser> userList) {
        this.userList = userList;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }
}
