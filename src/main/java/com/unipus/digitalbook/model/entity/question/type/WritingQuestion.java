package com.unipus.digitalbook.model.entity.question.type;

import com.unipus.digitalbook.model.entity.question.IQuestion;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.UserAnswer;

import java.math.BigDecimal;

/**
 * 写作题
 */
public class WritingQuestion extends Question implements IQuestion {
    @Override
    public BigDecimal doScore(double accuracy) {
        return getScore().multiply(BigDecimal.valueOf(accuracy));
    }

    @Override
    public double judge(UserAnswer userAnswer) {
        return getJudgeStrategy().judge(this, userAnswer);
    }
}
