package com.unipus.digitalbook.model.entity.question.type;

import com.unipus.digitalbook.model.entity.question.IQuestion;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.UserAnswer;

import java.math.BigDecimal;

/**
 * 角色扮演题
 */
public class RolePlayQuestion extends Question implements IQuestion {

    @Override
    public BigDecimal doScore(double accuracy) {
        if (accuracy == 1) {
            return getScore();
        }
        if (accuracy == 0) {
            return BigDecimal.ZERO;
        }
        return getScore().multiply(BigDecimal.valueOf(accuracy));
    }

    @Override
    public double judge(UserAnswer userAnswer) {
        return getJudgeStrategy().judge(this, userAnswer);
    }
}
