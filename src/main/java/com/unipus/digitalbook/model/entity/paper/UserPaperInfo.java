package com.unipus.digitalbook.model.entity.paper;

import com.unipus.digitalbook.model.enums.PaperSubmitStatusEnum;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import com.unipus.digitalbook.model.enums.UnitTestModeEnum;
import com.unipus.digitalbook.model.po.paper.PaperRoundPO;
import com.unipus.digitalbook.model.po.paper.PaperScoreBatchPO;

import java.math.BigDecimal;

/**
 * 用户试卷成绩信息
 */
public class UserPaperInfo {
    // 试卷成绩批次号
    private String scoreBatchId;
    // 试卷实例ID
    private String instanceId;
    // 试卷ID
    private String paperId;
    // 试卷版本号
    private String versionNumber;
    // 试卷类型
    private PaperTypeEnum paperType;
    // 用户ID
    private String openId;
    // 租户ID
    private Long tenantId;
    // 用户得分
    private BigDecimal userScore;
    // 标准得分
    private BigDecimal standardScore;
    // 试卷状态：0-未提交，1-已提交
    private Integer status;
    // 诊断卷测试模式
    private UnitTestModeEnum testMode;

    public UserPaperInfo(){}

    public UserPaperInfo(PaperInstance paperInstance) {
        this.scoreBatchId = paperInstance.getScoreBatchId();
        this.instanceId = paperInstance.getInstanceId();
        this.paperId = paperInstance.getPaperId();
        this.versionNumber = paperInstance.getVersionNumber();
        this.paperType = paperInstance.getPaperType();
        this.openId = paperInstance.getOpenId();
        this.tenantId = paperInstance.getTenantId();
        this.standardScore = paperInstance.getTotalScore();
    }

    public static UserPaperInfo build(PaperInstance paperInstance, BigDecimal userScore, PaperSubmitStatusEnum submitStatus) {
        UserPaperInfo paperInfo = new UserPaperInfo(paperInstance);
        paperInfo.setStatus(submitStatus.getCode());
        paperInfo.setUserScore(userScore);
        paperInfo.setStandardScore(paperInstance.getTotalScore());
        return paperInfo;
    }

    /**
     * 构建试卷最佳成绩信息
     * @param paperScoreBatchPO 批次信息
     * @param bestRound 最佳试卷成绩轮次信息
     * @return 试卷最佳成绩信息
     */
    public static UserPaperInfo buildBestUserPaperScoreInfo(PaperScoreBatchPO paperScoreBatchPO, PaperRoundPO bestRound){
        UserPaperInfo userPaperInfo = new UserPaperInfo();
        userPaperInfo.setPaperId(paperScoreBatchPO.getPaperId());
        userPaperInfo.setVersionNumber(paperScoreBatchPO.getPaperVersionNumber());
        userPaperInfo.setPaperType(PaperTypeEnum.getByCode(paperScoreBatchPO.getPaperType()));
        userPaperInfo.setInstanceId(bestRound.getId());
        userPaperInfo.setOpenId(paperScoreBatchPO.getOpenId());
        userPaperInfo.setTenantId(paperScoreBatchPO.getTenantId());
        userPaperInfo.setStatus(PaperSubmitStatusEnum.SUBMITTED.getCode());
        userPaperInfo.setScoreBatchId(paperScoreBatchPO.getId());
        userPaperInfo.setUserScore(bestRound.getUserScore());
        return userPaperInfo;
    }

    public String getScoreBatchId() {
        return scoreBatchId;
    }

    public void setScoreBatchId(String scoreBatchId) {
        this.scoreBatchId = scoreBatchId;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public PaperTypeEnum getPaperType() {
        return paperType;
    }

    public void setPaperType(PaperTypeEnum paperType) {
        this.paperType = paperType;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public BigDecimal getUserScore() {
        return userScore;
    }

    public void setUserScore(BigDecimal userScore) {
        this.userScore = userScore;
    }

    public BigDecimal getStandardScore() {
        return standardScore;
    }

    public void setStandardScore(BigDecimal standardScore) {
        this.standardScore = standardScore;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public UnitTestModeEnum getTestMode() {
        return testMode;
    }

    public void setTestMode(UnitTestModeEnum testMode) {
        this.testMode = testMode;
    }
}
