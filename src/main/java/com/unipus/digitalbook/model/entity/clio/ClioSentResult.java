package com.unipus.digitalbook.model.entity.clio;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * clio句子评测结果
 */
@Data
public class ClioSentResult {

    @Schema(description = "最终评测结果")
    private FinalResult finalResult;
    @Schema(description = "额外提示信息")
    private ExtraTips extraTips;

    @Data
    @Schema(description = "最终评测结果详情")
    public static class FinalResult {
        @Schema(description = "音频及数据对应的唯一标识")
        private String uuid;
        @Schema(description = "用户ID")
        private String userId;
        @Schema(description = "评测结果")
        private Result result;
        @Schema(description = "音频地址")
        private String url;
        @Schema(description = "参数信息")
        private Params params;
    }

    @Data
    @Schema(description = "评测结果详情")
    public static class Result {
        @Schema(description = "句子总分")
        private Integer total;
        @Schema(description = "流利度")
        private Fluency fluency;
        @Schema(description = "完整度")
        private Integer completeness;
        @Schema(description = "发音准确度")
        private Integer accuracy;
        @Schema(description = "韵律得分")
        private Integer rhythm;
        @Schema(description = "详细信息")
        private String detail;
        @Schema(description = "详情列表")
        private List<Detail> details;
        @Schema(description = "音频时长")
        private Double audioTime;
        @Schema(description = "单词详情列表")
        private List<WordDetail> wordDetails;
    }

    @Data
    @Schema(description = "流利度详情")
    public static class Fluency {
        @Schema(description = "停顿次数")
        private Integer pause;
        @Schema(description = "流利度得分")
        private Integer overall;
        @Schema(description = "每分钟读的单词数")
        private Integer speed;
    }

    @Data
    @Schema(description = "详情信息")
    public static class Detail {
        @Schema(description = "文本内容")
        private String text;
        @Schema(description = "得分")
        private Integer score;
        @Schema(description = "开始索引")
        private Integer beginindex;
        @Schema(description = "结束索引")
        private Integer endindex;
        @Schema(description = "单词详情列表")
        private List<WordDetail> wordDetails;
    }

    @Data
    @Schema(description = "单词详情")
    public static class WordDetail {
        @Schema(description = "单词得分")
        private Integer score;
        @Schema(description = "单词")
        private String word;
        @Schema(description = "单词在评测词典中的收录状态，值有0,1,2。0，表示该单词未被收录在评测词典中，引擎无法自动生成标准发音。1，表示该单词被收录在评测词典中。2，表示该单词未被收录在评测词典中，标准发音由引擎自动生成")
        private Integer indict;
        @Schema(description = "将transcript作为一个字符数组，beginindex表示单词的首字符在transcript中的下标，下标从0开始，为了方便应用层展示回显transcript")
        private Integer beginindex;
        @Schema(description = "将transcript作为一个字符数组，endindex表示单词的末字符在transcript中的下标，下标从0开始，为了方便应用层展示回显transcript")
        private Integer endindex;
    }

    @Data
    @Schema(description = "参数信息")
    public static class Params {
        @Schema(description = "请求参数")
        private Request request;
        @Schema(description = "音频参数")
        private Audio audio;
    }

    @Data
    @Schema(description = "请求参数详情")
    public static class Request {
        @Schema(description = "结果配置")
        private RequestResult result;
        @Schema(description = "转录文本")
        private String transcript;
        @Schema(description = "API名称")
        private String apiName;
        @Schema(description = "排名")
        private Integer rank;
        @Schema(description = "用户ID")
        private String userId;
        @Schema(description = "签名")
        private String sig;
    }

    @Data
    @Schema(description = "请求结果配置")
    public static class RequestResult {
        @Schema(description = "详情配置")
        private Details details;
    }

    @Data
    @Schema(description = "详情配置")
    public static class Details {
        @Schema(description = "调整参数")
        private Integer adjust;
    }

    @Data
    @Schema(description = "音频参数")
    public static class Audio {
        @Schema(description = "音频类型")
        private String audioType;
        @Schema(description = "声道数")
        private Integer channel;
        @Schema(description = "采样率")
        private Integer sampleRate;
        @Schema(description = "采样字节数")
        private Integer sampleBytes;
    }

    @Data
    @Schema(description = "额外提示信息详情")
    public static class ExtraTips {
        @Schema(description = "IP地址")
        private String ip;
        @Schema(description = "端口")
        private Integer port;
        @Schema(description = "开始时间")
        private String beginTime;
        @Schema(description = "WebSocket发送时间")
        private String wsSendTime;
        @Schema(description = "GOP开始时间")
        private String gopStartTime;
        @Schema(description = "音频结束时间")
        private String audioEndTime;
        @Schema(description = "GOP结束时间")
        private String gopEndTime;
        @Schema(description = "评分结束时间")
        private String scoreEndTime;
    }
}
