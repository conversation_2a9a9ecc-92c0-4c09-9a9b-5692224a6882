package com.unipus.digitalbook.model.entity.question.type;

import com.unipus.digitalbook.model.entity.question.IQuestion;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.UserAnswer;

import java.math.BigDecimal;

/**
 * 填空题
 */
public class FillBlankQuestion extends Question implements IQuestion {

    @Override
    public BigDecimal doScore(double accuracy) {
        if (accuracy == 1) {
            return getScore();
        }
        return BigDecimal.ZERO;
    }

    @Override
    public double judge(UserAnswer userAnswer) {
        return getJudgeStrategy().judge(this, userAnswer);
    }
}
