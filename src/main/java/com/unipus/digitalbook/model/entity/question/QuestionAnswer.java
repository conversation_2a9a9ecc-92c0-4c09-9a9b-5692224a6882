package com.unipus.digitalbook.model.entity.question;

import java.util.Objects;

/**
 * 题目答案
 */
public class QuestionAnswer {
    private Long id;

    /**
     * 题目ID
     */
    private Long questionId;

    private String correctAnswerId;

    /**
     * 正确答案文本
     */
    private String correctAnswerText;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    private Boolean enable;

    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        QuestionAnswer that = (QuestionAnswer) o;
        return Objects.equals(correctAnswerText, that.correctAnswerText) &&
                Objects.equals(correctAnswerId, that.correctAnswerId) &&
                Objects.equals(sortOrder, that.sortOrder);
    }
    @Override
    public int hashCode() {
        return Objects.hash(correctAnswerText, sortOrder);
    }
    public Long getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    public String getCorrectAnswerText() {
        return correctAnswerText;
    }

    public void setCorrectAnswerText(String correctAnswerText) {
        this.correctAnswerText = correctAnswerText;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCorrectAnswerId() {
        return correctAnswerId;
    }

    public void setCorrectAnswerId(String correctAnswerId) {
        this.correctAnswerId = correctAnswerId;
    }
}