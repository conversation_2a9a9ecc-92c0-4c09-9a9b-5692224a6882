package com.unipus.digitalbook.model.entity.chapter;

/**
 * 章节内容中的header节点，如h1,h2...h6
 *
 * <AUTHOR>
 * @date 2025/4/21 1:44 PM
 */
public class HeaderNode {

    /**
     * 前端标识
     */
    private int key;
    /**
     * header内的文本内容，也就是章节内容的小标题
     */
    private String text;
    /**
     * header类型，如h1,h2...h6
     */
    private String type;
    /**
     * header的id，也就是章节内容的小标题的id
     */
    private String id;


    // 无参构造函数
    public HeaderNode() {
        super();
    }

    // 有参构造函数
    public HeaderNode(int key, String text, String type, String id) {
        this.key = key;
        this.text = text;
        this.type = type;
        this.id = id;
    }

    // Getter 和 Setter 方法
    public int getKey() {
        return key;
    }

    public void setKey(int key) {
        this.key = key;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 重写toString方法以便于调试和日志输出
    @Override
    public String toString() {
        return "HeaderNode{" +
                "key=" + key +
                ", text='" + text + '\'' +
                ", type='" + type + '\'' +
                ", id='" + id + '\'' +
                '}';
    }
}

