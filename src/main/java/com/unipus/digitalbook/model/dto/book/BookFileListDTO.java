package com.unipus.digitalbook.model.dto.book;

import com.unipus.digitalbook.model.entity.book.BookFile;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class BookFileListDTO implements Serializable {
    List<BookFileDTO> bookFileList;

    public List<BookFileDTO> getBookFileList() {
        return bookFileList;
    }

    public void setBookFileList(List<BookFileDTO> bookFileList) {
        this.bookFileList = bookFileList;
    }

    public BookFileListDTO() {
        super();
    }

    public BookFileListDTO(List<BookFile> bookFileList) {
        super();
        if (bookFileList == null) {
            this.bookFileList = new ArrayList<>();
            return;
        }
        this.bookFileList = bookFileList.stream().map(BookFileDTO::new).toList();
    }
}

