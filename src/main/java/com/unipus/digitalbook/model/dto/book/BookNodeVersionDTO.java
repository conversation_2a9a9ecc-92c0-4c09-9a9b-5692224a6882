package com.unipus.digitalbook.model.dto.book;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "教材节点的版本信息")
public class BookNodeVersionDTO implements Serializable {
    @Schema(description = "资源类型编码: 1：教材基本信息/2：教材简介/3：版权信息/4：配套资源")
    private Integer typeCode;

    @Schema(description = "教材节点的资源id")
    private String resourceId;

    @Schema(description = "教材节点的版本id")
    private Long versionId;

    @Schema(description = "教材节点的版本号")
    private String versionNumber;


    public Integer getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(Integer typeCode) {
        this.typeCode = typeCode;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public Long getVersionId() {
        return versionId;
    }

    public void setVersionId(Long versionId) {
        this.versionId = versionId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }
}
