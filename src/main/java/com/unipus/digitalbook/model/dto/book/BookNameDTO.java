package com.unipus.digitalbook.model.dto.book;

import com.unipus.digitalbook.model.entity.book.Book;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 教材名称
 */
@Schema(description = "教材名称")
public class BookNameDTO implements Serializable {
    @Schema(description = "教材ID")
    private String bookId;

    @Schema(description = "教材名称")
    private String bookName;

    public BookNameDTO() {
    }

    public BookNameDTO(Book book) {
        if (book == null) {
            return;
        }
        this.bookId = book.getId();
        this.bookName = book.getChineseName();
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getBookName() {
        return bookName;
    }

    public void setBookName(String bookName) {
        this.bookName = bookName;
    }
}
