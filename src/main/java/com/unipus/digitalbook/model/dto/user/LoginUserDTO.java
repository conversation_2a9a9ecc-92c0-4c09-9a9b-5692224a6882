package com.unipus.digitalbook.model.dto.user;

import com.unipus.digitalbook.model.dto.organization.OrgInfoDTO;
import com.unipus.digitalbook.model.entity.CurrentUserInfo;
import com.unipus.digitalbook.model.entity.OrgInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;

@Schema(description = "当前用户登录信息对象")
public class LoginUserDTO implements Serializable {

    @Schema(description = "用户Id")
    private Long id;

    @Schema(description = "ssoId")
    private String ssoId;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "手机号")
    private String cellPhone;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "性别")
    private Integer gender;

    @Schema(description = "用户描述")
    private String desc;

    @Schema(description = "头像地址")
    private String avatarUrl;

    @Schema(description = "用户所属组织列表")
    private List<OrgInfoDTO> orgInfoList;

    @Schema(description = "AccessToken")
    private String token;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getSsoId() {
        return ssoId;
    }

    public void setSsoId(String ssoId) {
        this.ssoId = ssoId;
    }

    public List<OrgInfoDTO> getOrgInfoList() {
        return orgInfoList;
    }

    public void setOrgInfoList(List<OrgInfoDTO> orgInfoList) {
        this.orgInfoList = orgInfoList;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getCellPhone() {
        return cellPhone;
    }

    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public static LoginUserDTO build(CurrentUserInfo currentUserInfo, String token) {
        LoginUserDTO loginUserDTO = new LoginUserDTO();

        loginUserDTO.setId(currentUserInfo.getUserInfo().getId());
        loginUserDTO.setSsoId(currentUserInfo.getUserInfo().getSsoId());
        loginUserDTO.setName(currentUserInfo.getUserInfo().getName());
        loginUserDTO.setCellPhone(currentUserInfo.getUserInfo().getCellPhone());
        loginUserDTO.setEmail(currentUserInfo.getUserInfo().getEmail());
        loginUserDTO.setGender(currentUserInfo.getUserInfo().getGender());
        loginUserDTO.setDesc(currentUserInfo.getUserInfo().getDesc());
        loginUserDTO.setAvatarUrl(currentUserInfo.getUserInfo().getAvatarUrl());
        List<OrgInfo> orgInfoList = currentUserInfo.getOrgInfoList();
        if(CollectionUtils.isNotEmpty(orgInfoList)) {
            loginUserDTO.setOrgInfoList(orgInfoList.stream().map(OrgInfoDTO::new).toList());
        }
        loginUserDTO.setToken(token);
        return loginUserDTO;
    }

}