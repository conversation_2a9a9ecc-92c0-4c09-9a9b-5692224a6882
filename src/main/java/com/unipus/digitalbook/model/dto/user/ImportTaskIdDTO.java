package com.unipus.digitalbook.model.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "taskId信息")
public class ImportTaskIdDTO implements Serializable {
    private Long taskId;

    public ImportTaskIdDTO(){}

    public ImportTaskIdDTO(Long id) {
        this.taskId = id;
    }
    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }
}
