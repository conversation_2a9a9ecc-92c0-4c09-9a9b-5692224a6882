package com.unipus.digitalbook.model.dto.book;

import com.unipus.digitalbook.model.entity.book.Theme;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 主题列表对象
 */
@Schema(description = "主题列表对象")
public class ThemeListDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主题对象列表")
    private List<ThemeDTO> themeDTOList;

    public ThemeListDTO(List<Theme> themes) {
        this.themeDTOList = themes.stream().map(ThemeDTO::build).toList();
    }

    public List<ThemeDTO> getThemeDTOList() {
        return themeDTOList;
    }

    public void setThemeDTOList(List<ThemeDTO> themeDTOList) {
        this.themeDTOList = themeDTOList;
    }
}
