package com.unipus.digitalbook.model.dto.book;

import com.unipus.digitalbook.model.entity.permission.ResourceUser;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema(description = "资源用户列表")
public class BookUserListDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "用户ID列表")
    private List<BookUserDTO> bookUserDTOList;

    public BookUserListDTO() {}

    public BookUserListDTO(List<ResourceUser> resourceUserList) {
        this.bookUserDTOList = resourceUserList.stream().map(BookUserDTO::new).toList();
    }

    public static BookUserListDTO build(List<ResourceUser> resourceUserList) {
        BookUserListDTO bookUserListDTO = new BookUserListDTO();
        bookUserListDTO.setBookUserDTOList(resourceUserList.stream().map(BookUserDTO::new).toList());
        return bookUserListDTO;
    }

    public List<BookUserDTO> getBookUserDTOList() {
        return bookUserDTOList;
    }

    public void setBookUserDTOList(List<BookUserDTO> bookUserDTOList) {
        this.bookUserDTOList = bookUserDTOList;
    }
}
