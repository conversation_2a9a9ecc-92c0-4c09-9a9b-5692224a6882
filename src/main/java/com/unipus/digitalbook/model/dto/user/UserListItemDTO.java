package com.unipus.digitalbook.model.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;
import java.util.StringJoiner;

public class UserListItemDTO {
    @Schema(description = "用户id", example = "1")
    private Long id;

    @Schema(description = "用户姓名", example = "张三")
    private String name;

    @Schema(description = "手机号", example = "13800138000")
    private String cellPhone;

    @Schema(description = "所属机构列表")
    private List<String> organizationList;

    @Schema(description = "角色列表")
    private List<String> roleList;

    @Schema(description = "激活状态", example = "true")
    private Boolean activated;

    @Schema(description = "激活时间", example = "2023-10-23T08:00:00Z")
    private Date activationTime;

    @Schema(description = "创建时间", example = "2023-10-23T08:00:00Z")
    private Date createTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCellPhone() {
        return cellPhone;
    }

    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }

    public List<String> getOrganizationList() {
        return organizationList;
    }

    public void setOrganizationList(List<String> organizationList) {
        this.organizationList = organizationList;
    }

    public List<String> getRoleList() {
        return roleList;
    }

    public void setRoleList(List<String> roleList) {
        this.roleList = roleList;
    }

    public Boolean getActivated() {
        return activated;
    }

    public void setActivated(Boolean activated) {
        this.activated = activated;
    }

    public Date getActivationTime() {
        return activationTime;
    }

    public void setActivationTime(Date activationTime) {
        this.activationTime = activationTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", UserListItemDTO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("name='" + name + "'")
                .add("cellPhone='" + cellPhone + "'")
                .add("organizationList=" + organizationList)
                .add("roleList=" + roleList)
                .add("activated=" + activated)
                .add("activationTime=" + activationTime)
                .add("createTime=" + createTime)
                .toString();
    }
}
