package com.unipus.digitalbook.model.dto.feishu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 飞书卡片消息
 */
@Data
public class FeishuCardMessage {
    
    /**
     * 卡片版本
     */
    private String schema = "2.0";
    
    /**
     * 卡片配置
     */
    private CardConfig config;
    
    /**
     * 卡片主体
     */
    private CardBody body;
    
    /**
     * 卡片头部
     */
    private CardHeader header;
    
    @Data
    public static class CardConfig {
        /**
         * 是否允许多次更新
         */
        @JsonProperty("update_multi")
        private boolean updateMulti;
        
        /**
         * 样式配置
         */
        private CardStyle style;
    }
    
    @Data
    public static class CardStyle {
        /**
         * 文本大小配置
         */
        @JsonProperty("text_size")
        private TextSizeConfig textSize;
    }
    
    @Data
    public static class TextSizeConfig {
        /**
         * 普通文本大小配置
         */
        @JsonProperty("normal_v2")
        private TextSizeV2 normalV2;
    }
    
    @Data
    public static class TextSizeV2 {
        /**
         * 默认大小
         */
        @JsonProperty("default")
        private String defaultSize;
        
        /**
         * PC端大小
         */
        private String pc;
        
        /**
         * 移动端大小
         */
        private String mobile;
    }
    
    @Data
    public static class CardBody {
        /**
         * 布局方向
         */
        private String direction;
        
        /**
         * 水平间距
         */
        @JsonProperty("horizontal_spacing")
        private String horizontalSpacing;
        
        /**
         * 垂直间距
         */
        @JsonProperty("vertical_spacing")
        private String verticalSpacing;
        
        /**
         * 水平对齐
         */
        @JsonProperty("horizontal_align")
        private String horizontalAlign;
        
        /**
         * 垂直对齐
         */
        @JsonProperty("vertical_align")
        private String verticalAlign;
        
        /**
         * 内边距
         */
        private String padding;
        
        /**
         * 元素列表
         */
        private List<Object> elements;
    }
    
    @Data
    public static class CardElement {
        /**
         * 元素标签
         */
        private String tag;
        
        /**
         * 文本内容
         */
        private CardText text;
        
        /**
         * 元素内容
         */
        private String content;
        
        /**
         * 文本对齐
         */
        @JsonProperty("text_align")
        private String textAlign;
        
        /**
         * 文本大小
         */
        @JsonProperty("text_size")
        private String textSize;
        
        /**
         * 外边距
         */
        private String margin;
        
        /**
         * 按钮类型
         */
        private String type;
        
        /**
         * 按钮宽度
         */
        private String width;
        
        /**
         * 按钮大小
         */
        private String size;
        
        /**
         * 图标
         */
        private CardIcon icon;
        
        /**
         * 行为配置
         */
        private List<CardBehavior> behaviors;
    }
    
    @Data
    public static class CardText {
        /**
         * 文本标签
         */
        private String tag;
        
        /**
         * 文本内容
         */
        private String content;
    }
    
    @Data
    public static class CardIcon {
        /**
         * 图标标签
         */
        private String tag;
        
        /**
         * 图标标识
         */
        private String token;
        
        /**
         * 图标的颜色
         */
        private String color;
        
        /**
         * 自定义前缀图标的图片 key
         */
        @JsonProperty("img_key")
        private String imgKey;
        
        /**
         * 图标的尺寸
         */
        private String size;
    }
    
    @Data
    public static class CardBehavior {
        /**
         * 行为类型
         */
        private String type;
        
        /**
         * 默认URL
         */
        @JsonProperty("default_url")
        private String defaultUrl;
        
        /**
         * PC端URL
         */
        @JsonProperty("pc_url")
        private String pcUrl;
        
        /**
         * iOS端URL
         */
        @JsonProperty("ios_url")
        private String iosUrl;
        
        /**
         * Android端URL
         */
        @JsonProperty("android_url")
        private String androidUrl;
    }
    
    @Data
    public static class CardHeader {
        /**
         * 标题
         */
        private CardTitle title;
        
        /**
         * 副标题
         */
        private CardTitle subtitle;
        
        /**
         * 模板
         */
        private String template;
        
        /**
         * 图标
         */
        private CardIcon icon;
        
        /**
         * 内边距
         */
        private String padding;
    }
    
    @Data
    public static class CardTitle {
        /**
         * 标题标签
         */
        private String tag;
        
        /**
         * 标题内容
         */
        private String content;
    }
    
    @Data
    public static class CollapsiblePanel {
        /**
         * 元素标签
         */
        private String tag = "collapsible_panel";
        
        /**
         * 操作组件的唯一标识
         */
        @JsonProperty("element_id")
        private String elementId;
        
        /**
         * 面板内组件的排列方向
         */
        private String direction;
        
        /**
         * 面板内组件的垂直间距
         */
        @JsonProperty("vertical_spacing")
        private String verticalSpacing;
        
        /**
         * 面板内组件的水平间距
         */
        @JsonProperty("horizontal_spacing")
        private String horizontalSpacing;
        
        /**
         * 面板内组件的垂直居中方式
         */
        @JsonProperty("vertical_align")
        private String verticalAlign;
        
        /**
         * 面板内组件的水平居中方式
         */
        @JsonProperty("horizontal_align")
        private String horizontalAlign;
        
        /**
         * 折叠面板的内边距
         */
        private String padding;
        
        /**
         * 折叠面板的外边距
         */
        private String margin;
        
        /**
         * 面板是否展开
         */
        private boolean expanded;
        
        /**
         * 折叠面板的背景色
         */
        @JsonProperty("background_color")
        private String backgroundColor;
        
        /**
         * 折叠面板的标题设置
         */
        private CollapsibleHeader header;
        
        /**
         * 边框设置
         */
        private CollapsibleBorder border;
        
        /**
         * 面板内的元素列表
         */
        private List<Object> elements;
    }
    
    @Data
    public static class CollapsibleHeader {
        /**
         * 标题文本设置
         */
        private CardTitle title;
        
        /**
         * 标题区的背景色
         */
        @JsonProperty("background_color")
        private String backgroundColor;
        
        /**
         * 标题区的垂直居中方式
         */
        @JsonProperty("vertical_align")
        private String verticalAlign;
        
        /**
         * 标题区的内边距
         */
        private String padding;
        
        /**
         * 标题区的位置
         */
        private String position;
        
        /**
         * 标题区的宽度
         */
        private String width;
        
        /**
         * 标题前缀图标
         */
        private CardIcon icon;
        
        /**
         * 图标的位置
         */
        @JsonProperty("icon_position")
        private String iconPosition;
        
        /**
         * 折叠面板展开时图标旋转的角度
         */
        @JsonProperty("icon_expanded_angle")
        private Integer iconExpandedAngle;
    }
    
    @Data
    public static class CollapsibleBorder {
        /**
         * 边框的颜色
         */
        private String color;
        
        /**
         * 圆角设置
         */
        @JsonProperty("corner_radius")
        private String cornerRadius;
    }
} 