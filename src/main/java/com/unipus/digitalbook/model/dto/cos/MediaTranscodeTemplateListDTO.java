package com.unipus.digitalbook.model.dto.cos;

import com.unipus.digitalbook.model.entity.cos.MediaTranscodeTemplate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * 音频转码模版列表DTO
 *
 * <AUTHOR>
 * @date 2025/01/14
 */
@Data
@Schema(description = "音频转码模版列表DTO")
public class MediaTranscodeTemplateListDTO implements Serializable {

    @Schema(description = "模版列表")
    private List<MediaTranscodeTemplateDTO> templateList;

    @Schema(description = "总数量")
    private Integer total;

    public MediaTranscodeTemplateListDTO() {
    }

    public MediaTranscodeTemplateListDTO(List<MediaTranscodeTemplate> mediaTranscodeTemplates) {
        if (CollectionUtils.isEmpty(mediaTranscodeTemplates)) {
            this.templateList = Collections.emptyList();
            this.total = 0;
        } else {
            this.templateList = mediaTranscodeTemplates.stream()
                    .map(template -> new MediaTranscodeTemplateDTO(template.getTemplateId(), template.getTemplateName()))
                    .toList();
            this.total = mediaTranscodeTemplates.size();
        }
    }
}
