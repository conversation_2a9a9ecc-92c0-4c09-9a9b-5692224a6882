package com.unipus.digitalbook.model.dto.paper;

import com.unipus.digitalbook.model.entity.paper.PaperReference;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;

@Schema(description = "用户试卷作答记录DTO")
public class PaperReferenceListDTO implements Serializable {

    @Schema(description = "试卷引用列表")
    private List<PaperReferenceDTO> paperReferenceList;

    public PaperReferenceListDTO(List<PaperReference> paperReferences){
        if(CollectionUtils.isEmpty(paperReferences)){
            return;
        }
        this.paperReferenceList = paperReferences.stream().map(PaperReferenceDTO::new).toList();
    }

    public List<PaperReferenceDTO> getPaperReferenceList() {
        return paperReferenceList;
    }

    public void setPaperReferenceList(List<PaperReferenceDTO> paperReferenceList) {
        this.paperReferenceList = paperReferenceList;
    }
}
