package com.unipus.digitalbook.model.dto.user;

import com.unipus.digitalbook.model.dto.organization.OrgInfoDTO;
import com.unipus.digitalbook.model.entity.CurrentUserInfo;
import com.unipus.digitalbook.model.entity.OrgInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Schema(description = "当前用户信息对象")
public class CurrentUserDTO implements Serializable {

    @Schema(description = "当前用户信息")
    private UserInfoDTO userInfoDTO;

    @Schema(description = "所属组织信息")
    private List<OrgInfoDTO> orgInfoList;

    @Schema(description = "用户所属组织角色映射")
    private Map<Long, Set<Long>> orgRolesMap;

    public UserInfoDTO getUserInfoDTO() {
        return userInfoDTO;
    }

    public void setUserInfoDTO(UserInfoDTO userInfoDTO) {
        this.userInfoDTO = userInfoDTO;
    }

    public Map<Long, Set<Long>> getOrgRolesMap() {
        return orgRolesMap;
    }

    public void setOrgRolesMap(Map<Long, Set<Long>> orgRolesMap) {
        this.orgRolesMap = orgRolesMap;
    }

    public List<OrgInfoDTO> getOrgInfoList() {
        return orgInfoList;
    }

    public void setOrgInfoList(List<OrgInfoDTO> orgInfoList) {
        this.orgInfoList = orgInfoList;
    }

    public static CurrentUserDTO build(CurrentUserInfo currentUserInfo) {
        CurrentUserDTO currentUserDTO = new CurrentUserDTO();
        currentUserDTO.setUserInfoDTO(new UserInfoDTO(currentUserInfo.getUserInfo()));
        List<OrgInfo> orgInfoList = currentUserInfo.getOrgInfoList();
        if(CollectionUtils.isNotEmpty(orgInfoList)) {
            currentUserDTO.setOrgInfoList(orgInfoList.stream().map(OrgInfoDTO::new).toList());
        }
        currentUserDTO.setOrgRolesMap(currentUserInfo.getOrgRolesMap());
        return currentUserDTO;
    }

}