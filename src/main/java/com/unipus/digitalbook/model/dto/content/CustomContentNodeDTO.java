package com.unipus.digitalbook.model.dto.content;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.unipus.digitalbook.model.entity.content.CustomContentNode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema(description = "章节内的节点数据的传输对象")
public class CustomContentNodeDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "章节唯一标识符")
    @JsonProperty("id")
    private String id;

    @Schema(description = "章节节点的文本内容")
    @JsonProperty("text")
    private String text;

    @Schema(description = "章节节点的类型")
    @JsonProperty("type")
    private String type;

    @Schema(description = "节点下的文字数量")
    @JsonProperty("wordCount")
    private Long wordCount;

    @Schema(description = "节点下的音频时长（单位：毫秒）")
    @JsonProperty("audioDuration")
    private Long audioDuration;

    @Schema(description = "节点下的视频时长（单位：毫秒）")
    @JsonProperty("videoDuration")
    private Long videoDuration;

    @Schema(description = "节点下的中日韩文字数量")
    private Long cjkWordCount;

    @Schema(description = "节点下的非中日韩文字数量")
    private Long nonCjkWordCount;

    @Schema(description = "节点下的题型")
    private String questionType;

    @Schema(description = "节点下的子节点列表")
    @JsonProperty("children")
    private List<CustomContentNodeDTO> children;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getWordCount() {
        return wordCount;
    }

    public void setWordCount(Long wordCount) {
        this.wordCount = wordCount;
    }

    public Long getAudioDuration() {
        return audioDuration;
    }

    public void setAudioDuration(Long audioDuration) {
        this.audioDuration = audioDuration;
    }

    public Long getVideoDuration() {
        return videoDuration;
    }

    public void setVideoDuration(Long videoDuration) {
        this.videoDuration = videoDuration;
    }

    public Long getCjkWordCount() {
        return cjkWordCount;
    }

    public void setCjkWordCount(Long cjkWordCount) {
        this.cjkWordCount = cjkWordCount;
    }

    public Long getNonCjkWordCount() {
        return nonCjkWordCount;
    }

    public void setNonCjkWordCount(Long nonCjkWordCount) {
        this.nonCjkWordCount = nonCjkWordCount;
    }

    public String getQuestionType() {
        return questionType;
    }

    public void setQuestionType(String questionType) {
        this.questionType = questionType;
    }

    public List<CustomContentNodeDTO> getChildren() {
        return children;
    }

    public void setChildren(List<CustomContentNodeDTO> children) {
        this.children = children;
    }

    public CustomContentNodeDTO() {
        super();
    }

    public CustomContentNodeDTO(CustomContentNode customContentNode) {
        this.id = customContentNode.getId();
        this.text = customContentNode.getText();
        this.type = customContentNode.getType();
        this.wordCount = customContentNode.getWordCount();
        this.audioDuration = customContentNode.getAudioDuration();
        this.videoDuration = customContentNode.getVideoDuration();
        this.cjkWordCount = customContentNode.getCjkWordCount();
        this.nonCjkWordCount = customContentNode.getNonCjkWordCount();
        this.questionType = customContentNode.getQuestionType();
        this.children = customContentNode.getChildren() == null ? null : customContentNode.getChildren().stream().map(CustomContentNodeDTO::new).toList();
    }
}


