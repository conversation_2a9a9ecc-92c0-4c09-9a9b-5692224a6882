package com.unipus.digitalbook.model.dto.feishu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 飞书富文本消息
 */
@Data
public class FeishuPostMessage {
    
    /**
     * 富文本内容
     */
    private PostContent post;
    
    @Data
    public static class PostContent {
        /**
         * 富文本内容
         */
        private PostContentDetail zh_cn;
    }
    
    @Data
    public static class PostContentDetail {
        /**
         * 标题
         */
        private String title;
        
        /**
         * 富文本内容列表 - 二维数组格式
         */
        private List<List<ContentItem>> content;
    }
    
    @Data
    public static class ContentItem {
        /**
         * 内容类型：text, image, a, at
         */
        private String tag;
        
        /**
         * 文本内容
         */
        private String text;
        
        /**
         * 链接地址
         */
        private String href;
        
        /**
         * 用户ID
         */
        @JsonProperty("user_id")
        private String userId;
        
        /**
         * 用户名
         */
        @JsonProperty("user_name")
        private String userName;
        
        /**
         * 图片地址
         */
        private String imageKey;
        
        /**
         * 图片宽度
         */
        private Integer width;
        
        /**
         * 图片高度
         */
        private Integer height;
    }
} 