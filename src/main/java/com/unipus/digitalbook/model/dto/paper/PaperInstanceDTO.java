package com.unipus.digitalbook.model.dto.paper;

import com.unipus.digitalbook.model.dto.question.BigQuestionGroupDTO;
import com.unipus.digitalbook.model.entity.paper.PaperInstance;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 试卷实例DTO
 */
@Schema(description = "试卷实例DTO")
public class PaperInstanceDTO implements Serializable {
    @Schema(description = "试卷实例ID（轮次ID）")
    private String instanceId;
    @Schema(description = "试卷ID")
    private String paperId;
    @Schema(description = "试卷类型")
    private Integer paperType;
    @Schema(description = "试卷名称")
    private String paperName;
    @Schema(description = "试卷版本号")
    private String versionNumber;
    @Schema(description = "试卷说明")
    private String description;
    @Schema(description = "题目数量")
    private Integer questionCount;
    @Schema(description = "试卷总分")
    private BigDecimal totalScore;
    @Schema(description = "试卷内容")
    private String content;
    @Schema(description = "题目列表")
    private List<BigQuestionGroupDTO> bigQuestionGroupDTOs;

    public PaperInstanceDTO(PaperInstance paperInstance, boolean isReturnAnswer) {
        this.instanceId = paperInstance.getInstanceId();
        this.paperId = paperInstance.getPaperId();
        this.paperType = paperInstance.getPaperType().getCode();
        this.paperName = paperInstance.getPaperName();
        this.versionNumber = paperInstance.getVersionNumber();
        this.description = paperInstance.getDescription();
        this.questionCount = paperInstance.getQuestionCount();
        this.totalScore = paperInstance.getTotalScore();
        this.content = paperInstance.getContent();
        if(!CollectionUtils.isEmpty(paperInstance.getBigQuestionGroupList())) {
            this.bigQuestionGroupDTOs = paperInstance.getBigQuestionGroupList().stream()
                    .map(group -> new BigQuestionGroupDTO(group, isReturnAnswer)).toList();
        }
    }

    public static PaperInstanceDTO build(PaperInstance paperInstance, boolean isReturnAnswer) {
        if(paperInstance==null) {
            return null;
        }else{
            return new PaperInstanceDTO(paperInstance, isReturnAnswer);
        }
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public Integer getPaperType() {
        return paperType;
    }

    public void setPaperType(Integer paperType) {
        this.paperType = paperType;
    }

    public String getPaperName() {
        return paperName;
    }

    public void setPaperName(String paperName) {
        this.paperName = paperName;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getQuestionCount() {
        return questionCount;
    }

    public void setQuestionCount(Integer questionCount) {
        this.questionCount = questionCount;
    }

    public BigDecimal getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(BigDecimal totalScore) {
        this.totalScore = totalScore;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<BigQuestionGroupDTO> getBigQuestionGroupDTOs() {
        return bigQuestionGroupDTOs;
    }

    public void setBigQuestionGroupDTOs(List<BigQuestionGroupDTO> bigQuestionGroupDTOs) {
        this.bigQuestionGroupDTOs = bigQuestionGroupDTOs;
    }
}
