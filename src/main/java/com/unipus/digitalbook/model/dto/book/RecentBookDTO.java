package com.unipus.digitalbook.model.dto.book;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
@Schema(description = "最近书籍的数据传输对象")
public class RecentBookDTO implements Serializable {

    // 书籍的唯一标识符
    @Schema(description = "书籍的唯一标识符", example = "1234567890")
    private Long id;

    // 教材封面URL
    @Schema(description = "教材封面URL", example = "http://example.com/cover.jpg")
    private String coverUrl;

    // 教材名称
    @Schema(description = "教材名称", example = "Java基础教程")
    private String name;

    // 编辑状态
    @Schema(description = "编辑状态", example = "已出版")
    private String editStatus;

    // 是否有编辑权限
    @Schema(description = "是否有编辑权限", example = "true")
    private Boolean editPermission;

    // 最新编辑时间
    @Schema(description = "最新编辑时间", example = "2023-07-08T12:30:45")
    private LocalDateTime lastEditedTime;

    // 编者头像列表（创建者）
    @Schema(description = "编者头像列表（创建者）", example = "[\"http://example.com/avatar1.jpg\", \"http://example.com/avatar2.jpg\"]")
    private List<String> authorAvatars;

    @Schema(description = "编者", example = "李书易")
    private List<String> author;

    // 协作者头像列表
    @Schema(description = "协作者头像列表", example = "[\"http://example.com/collaborator1.jpg\", \"http://example.com/collaborator2.jpg\"]")
    private List<String> collaboratorAvatars;

    @Schema(description = "协作者列表")
    private List<String> collaboratorList;

    public Long getId() {
        return id;
    }

    public RecentBookDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getCoverUrl() {
        return coverUrl;
    }

    public RecentBookDTO setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
        return this;
    }

    public String getName() {
        return name;
    }

    public RecentBookDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getEditStatus() {
        return editStatus;
    }

    public RecentBookDTO setEditStatus(String editStatus) {
        this.editStatus = editStatus;
        return this;
    }

    public Boolean getEditPermission() {
        return editPermission;
    }

    public RecentBookDTO setEditPermission(Boolean editPermission) {
        this.editPermission = editPermission;
        return this;
    }

    public LocalDateTime getLastEditedTime() {
        return lastEditedTime;
    }

    public RecentBookDTO setLastEditedTime(LocalDateTime lastEditedTime) {
        this.lastEditedTime = lastEditedTime;
        return this;
    }

    public List<String> getAuthorAvatars() {
        return authorAvatars;
    }

    public RecentBookDTO setAuthorAvatars(List<String> authorAvatars) {
        this.authorAvatars = authorAvatars;
        return this;
    }

    public List<String> getCollaboratorAvatars() {
        return collaboratorAvatars;
    }

    public RecentBookDTO setCollaboratorAvatars(List<String> collaboratorAvatars) {
        this.collaboratorAvatars = collaboratorAvatars;
        return this;
    }

    public List<String> getAuthor() {
        return author;
    }

    public RecentBookDTO setAuthor(List<String> author) {
        this.author = author;
        return this;
    }

    public List<String> getCollaboratorList() {
        return collaboratorList;
    }

    public RecentBookDTO setCollaboratorList(List<String> collaboratorList) {
        this.collaboratorList = collaboratorList;
        return this;
    }
}