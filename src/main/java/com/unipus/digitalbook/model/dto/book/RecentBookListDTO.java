package com.unipus.digitalbook.model.dto.book;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;


/**
 * RecentBookListDTO类表示最近书籍列表的数据传输对象。
 */
@Schema(description = "最近打开书籍列表")
public class RecentBookListDTO implements Serializable {

    private List<RecentBookDTO> recentBookList;

    /**
     * 获取最近书籍列表。
     *
     * @return recentBookList 最近书籍列表
     */
    @Schema(description = "最近书籍列表")
    public List<RecentBookDTO> getRecentBookList() {
        return recentBookList;
    }

    /**
     * 设置最近书籍列表。
     *
     * @param recentBookList 最近书籍列表
     * @return RecentBookListDTO 对象本身，用于链式调用
     */
    public RecentBookListDTO setRecentBookList(List<RecentBookDTO> recentBookList) {
        this.recentBookList = recentBookList;
        return this;
    }
}



