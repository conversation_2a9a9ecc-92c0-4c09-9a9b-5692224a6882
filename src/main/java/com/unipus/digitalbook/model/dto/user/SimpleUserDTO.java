package com.unipus.digitalbook.model.dto.user;

import com.unipus.digitalbook.common.utils.UserUtil;
import com.unipus.digitalbook.model.entity.UserInfo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 简略用户信息实体
 */
public class SimpleUserDTO implements Serializable {

    @Schema(description = "用户ID", example = "123456")
    private Long userId;

    @Schema(description = "用户姓名", example = "张三")
    private String userName;

    @Schema(description = "手机号", example = "139****4567")
    private String mobile;

    public SimpleUserDTO(UserInfo userInfo) {
        this.userId = userInfo.getId();
        this.userName = userInfo.getName();
        this.mobile = UserUtil.desensitization(userInfo.getCellPhone());
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

}
