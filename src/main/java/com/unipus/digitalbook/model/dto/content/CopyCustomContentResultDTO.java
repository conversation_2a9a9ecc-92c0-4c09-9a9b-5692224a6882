package com.unipus.digitalbook.model.dto.content;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Map;

@Schema(description = "复制自建内容结果DTO")
public class CopyCustomContentResultDTO implements Serializable {

    @Schema(description = "复制前后bizId的映射关系，key为原bizId，value为新bizId")
    private Map<String, String> bizIdMapping;

    public CopyCustomContentResultDTO() {
    }

    public CopyCustomContentResultDTO(Map<String, String> bizIdMapping) {
        this.bizIdMapping = bizIdMapping;
    }

    public Map<String, String> getBizIdMapping() {
        return bizIdMapping;
    }

    public void setBizIdMapping(Map<String, String> bizIdMapping) {
        this.bizIdMapping = bizIdMapping;
    }
}
