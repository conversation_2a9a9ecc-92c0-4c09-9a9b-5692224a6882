package com.unipus.digitalbook.model.dto.process;

import com.unipus.digitalbook.model.entity.action.ReadProgressList;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Set;

@Schema(description = "学习进度列表")
public class ReadProgressListDTO implements Serializable {
    @Schema(description = "已学节点列表")
    private Set<String> completedList;

    @Schema(description = "没有学习节点列表")
    private Set<String> uncompletedList;

    public ReadProgressListDTO() {
    }

    public ReadProgressListDTO(ReadProgressList learnProgressList) {
        if (learnProgressList == null) {
            return;
        }
        this.completedList = learnProgressList.getCompletedList();
        this.uncompletedList = learnProgressList.getUncompletedList();
    }

    public Set<String> getCompletedList() {
        return completedList;
    }

    public void setCompletedList(Set<String> completedList) {
        this.completedList = completedList;
    }

    public Set<String> getUncompletedList() {
        return uncompletedList;
    }

    public void setUncompletedList(Set<String> uncompletedList) {
        this.uncompletedList = uncompletedList;
    }
}
