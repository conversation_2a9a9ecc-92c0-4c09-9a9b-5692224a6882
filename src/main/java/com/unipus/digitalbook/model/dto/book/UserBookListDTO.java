package com.unipus.digitalbook.model.dto.book;

import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.entity.book.Book;
import com.unipus.digitalbook.model.entity.book.UserBook;
import com.unipus.digitalbook.model.entity.book.UserBookList;
import com.unipus.digitalbook.model.entity.permission.ResourceUser;
import com.unipus.digitalbook.service.UserService;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.*;

@Schema(description = "用户教材列表")
public class UserBookListDTO implements Serializable {
    @Schema(description = "教材列表")
    private List<UserBookDTO> userBooks;
    @Schema(description = "总数量")
    private Integer totalCount;

    public UserBookListDTO() {
        this.userBooks = Collections.emptyList();
        this.totalCount = 0;
    }

    public UserBookListDTO(UserBookList userBookList, UserService userService) {
        this.totalCount = userBookList.getTotalCount();
        List<UserBook> userBookEntityList = userBookList.getUserBooks();
        Set<Long> userIds = HashSet.newHashSet(userBookEntityList.size());
        userBookEntityList.forEach(userBook -> {
            Book book = userBook.getBook();
            if (book != null) {
                userIds.add(book.getEditorId());
            }
        });
        Map<Long, UserInfo> userMap = userService.getUserMap(userIds);
        this.userBooks = userBookList.getUserBooks()
                .stream().map(userBook -> new UserBookDTO(userBook, userMap)).toList();
    }

    public UserBookListDTO fillCollaborators(Map<String, List<ResourceUser>> bookEditorMap) {
        List<UserBookDTO> userBookList = this.userBooks;
        if (userBookList.isEmpty() || bookEditorMap.isEmpty()) {
            return this;
        }
        userBookList.forEach(userBook -> {
            List<ResourceUser> collaborators = bookEditorMap.get(userBook.getBook().getId());
            if (collaborators != null && userBook.getBook() != null) {
                userBook.getBook().setCollaborators(collaborators.stream().map(BookUserDTO::new).toList());
            }
        });
        return this;
    }

    public UserBookListDTO fillPublishFlag(Map<String, Boolean> publishFlagMap) {
        List<UserBookDTO> userBookList = this.userBooks;
        if (userBookList.isEmpty() || publishFlagMap.isEmpty()) {
            return this;
        }
        userBookList.forEach(userBook -> {
            Boolean publishFlag = publishFlagMap.get(userBook.getBook().getId());
            userBook.setPublishFlag(publishFlag);
        });
        return this;
    }
    public UserBookListDTO fillPublishTime(Map<String, Long> publishTimeMap) {
        List<UserBookDTO> userBookList = this.userBooks;
        if (userBookList.isEmpty() || publishTimeMap.isEmpty()) {
            return this;
        }
        userBookList.forEach(userBook -> {
            String bookId = userBook.getBook().getId();
            if (publishTimeMap.containsKey(bookId)){
                userBook.setPublishTime(publishTimeMap.get(bookId));
            }
        });
        return this;
    }

    public List<UserBookDTO> getUserBooks() {
        return userBooks;
    }

    public void setUserBooks(List<UserBookDTO> userBooks) {
        this.userBooks = userBooks;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }
}
