package com.unipus.digitalbook.model.dto.book;

import com.unipus.digitalbook.model.entity.book.BookBasic;
import com.unipus.digitalbook.model.entity.book.BookCopyright;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;

@Schema(description = "教材版权信息")
public class BookCopyrightDTO implements Serializable {
    @Schema(description = "当前实体id，应与版本id一致")
    private Long id;

    @Schema(description = "教材ID")
    private String bookId;

    @Schema(description = "教材名称")
    private String chineseName;

    @Schema(description = "教材英文名称")
    private String englishName;

    @Schema(description = "总主编")
    private String chiefEditor;

    @Schema(description = "主编")
    private String editor;

    @Schema(description = "副主编")
    private String deputyEditor;

    @Schema(description = "项目策划")
    private String projectPlanner;
    @Schema(description = "出版时间-年")
    private Integer publishYear;
    @Schema(description = "出版时间-月")
    private Integer publishMonth;
    @Schema(description = "出版时间-日")
    private Integer publishDay;
    @Schema(description = "责任编辑")
    private String executiveEditor;

    @Schema(description = "责任校对")
    private String proofreader;

    @Schema(description = "数字编辑")
    private String digitalEditor;

    @Schema(description = "封面设计")
    private String coverDesigner;

    @Schema(description = "版式设计")
    private String layoutDesigner;

    @Schema(description = "出版发行")
    private String publisher;

    @Schema(description = "字数")
    private String wordCount;

    @Schema(description = "ISBN")
    private String isbn;

    @Schema(description = "定价")
    private BigDecimal price;

    @Schema(description = "版次")
    private String edition;

    @Schema(description = "版本id")
    private Long versionId;
    @Schema(description = "版本号")
    private String versionNumber;

    public BookCopyrightDTO(BookCopyright bookCopyright, BookBasic bookBasic) {
        if (bookBasic != null) {
            this.chineseName = bookBasic.getChineseName();
            this.englishName = bookBasic.getEnglishName();
        }
        if (bookCopyright != null) {
            this.id = bookCopyright.getId();
            this.bookId = bookCopyright.getBookId();
            this.chiefEditor = bookCopyright.getChiefEditor();
            this.editor = bookCopyright.getEditor();
            this.deputyEditor = bookCopyright.getDeputyEditor();
            this.projectPlanner = bookCopyright.getProjectPlanner();
            this.publishYear = bookCopyright.getPublishYear();
            this.publishMonth = bookCopyright.getPublishMonth();
            this.publishDay = bookCopyright.getPublishDay();
            this.executiveEditor = bookCopyright.getExecutiveEditor();
            this.proofreader = bookCopyright.getProofreader();
            this.digitalEditor = bookCopyright.getDigitalEditor();
            this.coverDesigner = bookCopyright.getCoverDesigner();
            this.layoutDesigner = bookCopyright.getLayoutDesigner();
            this.publisher = bookCopyright.getPublisher();
            this.wordCount = bookCopyright.getWordCount();
            this.isbn = bookCopyright.getIsbn();
            this.price = bookCopyright.getPrice();
            this.edition = bookCopyright.getEdition();
            this.versionId = bookCopyright.getId();
            this.versionNumber = bookCopyright.getVersionNumber();
        }
    }

    public BookCopyrightDTO(BookCopyright bookCopyright) {
        this(bookCopyright, null);
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getChineseName() {
        return chineseName;
    }

    public void setChineseName(String chineseName) {
        this.chineseName = chineseName;
    }

    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }

    public String getChiefEditor() {
        return chiefEditor;
    }

    public void setChiefEditor(String chiefEditor) {
        this.chiefEditor = chiefEditor;
    }

    public String getEditor() {
        return editor;
    }

    public void setEditor(String editor) {
        this.editor = editor;
    }

    public String getDeputyEditor() {
        return deputyEditor;
    }

    public void setDeputyEditor(String deputyEditor) {
        this.deputyEditor = deputyEditor;
    }

    public String getProjectPlanner() {
        return projectPlanner;
    }

    public void setProjectPlanner(String projectPlanner) {
        this.projectPlanner = projectPlanner;
    }

    public Integer getPublishYear() {
        return publishYear;
    }

    public void setPublishYear(Integer publishYear) {
        this.publishYear = publishYear;
    }

    public Integer getPublishMonth() {
        return publishMonth;
    }

    public void setPublishMonth(Integer publishMonth) {
        this.publishMonth = publishMonth;
    }

    public Integer getPublishDay() {
        return publishDay;
    }

    public void setPublishDay(Integer publishDay) {
        this.publishDay = publishDay;
    }

    public String getExecutiveEditor() {
        return executiveEditor;
    }

    public void setExecutiveEditor(String executiveEditor) {
        this.executiveEditor = executiveEditor;
    }

    public String getProofreader() {
        return proofreader;
    }

    public void setProofreader(String proofreader) {
        this.proofreader = proofreader;
    }

    public String getDigitalEditor() {
        return digitalEditor;
    }

    public void setDigitalEditor(String digitalEditor) {
        this.digitalEditor = digitalEditor;
    }

    public String getCoverDesigner() {
        return coverDesigner;
    }

    public void setCoverDesigner(String coverDesigner) {
        this.coverDesigner = coverDesigner;
    }

    public String getLayoutDesigner() {
        return layoutDesigner;
    }

    public void setLayoutDesigner(String layoutDesigner) {
        this.layoutDesigner = layoutDesigner;
    }

    public String getPublisher() {
        return publisher;
    }

    public void setPublisher(String publisher) {
        this.publisher = publisher;
    }

    public String getWordCount() {
        return wordCount;
    }

    public void setWordCount(String wordCount) {
        this.wordCount = wordCount;
    }

    public String getIsbn() {
        return isbn;
    }

    public void setIsbn(String isbn) {
        this.isbn = isbn;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getEdition() {
        return edition;
    }

    public void setEdition(String edition) {
        this.edition = edition;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getVersionId() {
        return versionId;
    }

    public void setVersionId(Long versionId) {
        this.versionId = versionId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }
}
