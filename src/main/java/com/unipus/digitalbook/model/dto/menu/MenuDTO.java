package com.unipus.digitalbook.model.dto.menu;

import com.unipus.digitalbook.model.entity.menu.Menu;
import com.unipus.digitalbook.model.entity.menu.ParentMenu;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Schema(description = "菜单模型")
public class MenuDTO implements Serializable {
    @Schema(description = "菜单id", example = "1")
    private Long id;
    @Schema(description = "菜单名字", example = "一级菜单")
    private String name;
    @Schema(description = "菜单路径", example = "index")
    private String path;
    @Schema(description = "菜单排序", example = "1")
    private Integer position;
    @Schema(description = "启用状态", example = "1")
    private Integer status;
    @Schema(description = "菜单是否已分配", example = "true")
    private Boolean assigned;
    @Schema(description = "子菜单")
    private List<MenuDTO> subMenus = new ArrayList<>();
    @Schema(description = "父菜单")
    private ParentMenuDTO parentMenu;


    public MenuDTO() {
    }
    public MenuDTO(Menu menu) {
        this.id = menu.getId();
        this.name = menu.getName();
        this.path = menu.getPath();
        this.position = menu.getPosition();
        this.status = menu.getStatus();
        this.assigned = menu.getAssigned();
        ParentMenu parentMenuEntity = menu.getParentMenu();
        if (parentMenuEntity != null) {
            this.parentMenu = new ParentMenuDTO(parentMenuEntity.getId(), parentMenuEntity.getName());
        }
        if (!CollectionUtils.isEmpty(menu.getSubMenus())) {
            this.subMenus = menu.getSubMenus().stream().map(MenuDTO::new).toList();
        }
    }
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Integer getPosition() {
        return position;
    }

    public void setPosition(Integer position) {
        this.position = position;
    }

    public List<MenuDTO> getSubMenus() {
        return subMenus;
    }

    public void setSubMenus(List<MenuDTO> subMenus) {
        this.subMenus = subMenus;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Boolean getAssigned() {
        return assigned;
    }

    public void setAssigned(Boolean assigned) {
        this.assigned = assigned;
    }

    public ParentMenuDTO getParentMenu() {
        return parentMenu;
    }

    public void setParentMenu(ParentMenuDTO parentMenu) {
        this.parentMenu = parentMenu;
    }
}
