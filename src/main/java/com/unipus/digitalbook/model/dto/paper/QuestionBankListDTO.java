package com.unipus.digitalbook.model.dto.paper;

import com.unipus.digitalbook.model.entity.paper.QuestionBank;
import com.unipus.digitalbook.model.entity.paper.QuestionBankStat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

@Schema(description = "题库列表DTO")
public class QuestionBankListDTO implements Serializable {

    @Schema(description = "题库列表列表")
    private List<QuestionBankDTO> questionBankDTOs;

    @Schema(description = "题库统计信息")
    private QuestionBankStatDTO questionBankStatDTO;

    public QuestionBankListDTO(List<QuestionBank> questionBanks) {
        if (questionBanks.isEmpty()) { return; }
        // 构建题库列表
        this.questionBankDTOs = questionBanks.stream().map(QuestionBankDTO::new).toList();
        // 构建题库统计信息
        this.questionBankStatDTO = new QuestionBankStatDTO(QuestionBankStat.build(questionBanks));
    }

    public List<QuestionBankDTO> getQuestionBankDTOs() {
        return questionBankDTOs;
    }

    public void setQuestionBankDTOs(List<QuestionBankDTO> questionBankDTOs) {
        this.questionBankDTOs = questionBankDTOs;
    }

    public QuestionBankStatDTO getQuestionBankStatDTO() {
        return questionBankStatDTO;
    }

    public void setQuestionBankStatDTO(QuestionBankStatDTO questionBankStatDTO) {
        this.questionBankStatDTO = questionBankStatDTO;
    }

}
