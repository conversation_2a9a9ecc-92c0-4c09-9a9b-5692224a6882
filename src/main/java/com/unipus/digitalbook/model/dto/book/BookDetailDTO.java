package com.unipus.digitalbook.model.dto.book;

import com.unipus.digitalbook.model.dto.SeriesDTO;
import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.entity.book.Book;
import com.unipus.digitalbook.model.entity.book.BookBasic;
import com.unipus.digitalbook.model.entity.permission.ResourceUser;
import com.unipus.digitalbook.model.entity.publish.BookVersion;
import com.unipus.digitalbook.model.enums.BusinessTypeEnum;
import com.unipus.digitalbook.model.enums.CourseNatureEnum;
import com.unipus.digitalbook.model.enums.LanguageEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 教材详情
 * 教材的基本信息
 */
@Schema(description = "教材详情")
public class BookDetailDTO implements Serializable {
    @Schema(description = "教材ID")
    private String id;

    @Schema(description = "编者姓名")
    private BookUserDTO editor;

    @Schema(description = "教材中文名称")
    private String chineseName;

    @Schema(description = "教材英文名称")
    private String englishName;

    @Schema(description = "语种", example = "en")
    private LanguageEnum language;

    @Schema(description = "教材业务类型")
    private BusinessTypeEnum businessType;

    @Schema(description = "教材系列")
    private SeriesDTO series;

    @Schema(description = "对应课程")
    private String course;

    @Schema(description = "课程性质")
    private CourseNatureEnum courseNature;

    @Schema(description = "适用专业")
    private String applicableMajor;
    @Schema(description = "适用年级")
    private String applicableGrade;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "联系邮箱")
    private String contactEmail;

    @Schema(description = "PC端封面图片地址")
    private String pcCoverUrl;

    @Schema(description = "APP横版封面图片地址")
    private String appHorizontalCoverUrl;

    @Schema(description = "APP竖版封面图片地址")
    private String appVerticalCoverUrl;

    @Schema(description = "创建时间")
    private Long createTime;

    @Schema(description = "创建者")
    private BookUserDTO createBy;

    @Schema(description = "教材详细介绍")
    private String description;

    @Schema(description = "浅色")
    private String lightColor;

    @Schema(description = "深色")
    private String darkColor;

    @Schema(description = "协作者列表")
    private List<BookUserDTO> collaborators;

    @Schema(description = "否纯数字教材  0-否 1-是")
    private Boolean digitalFlag;

    @Schema(description = "教材上架时间")
    private Long publishTime;

    @Schema(description = "教材是否上架")
    private boolean publishFlag;
    /**
     * 教材版本
     */
    private BookVersion bookVersion;


    public BookDetailDTO() {
    }

    public BookDetailDTO(Book book, Map<Long, UserInfo> userMap) {
        this.id = book.getId();
        UserInfo editorInfo = userMap.get(book.getEditorId());
        if (editorInfo != null) {
            this.editor = new BookUserDTO(editorInfo);
        }
        UserInfo userInfo = userMap.get(book.getCreateBy());
        if (userInfo != null) {
            this.createBy = new BookUserDTO(userInfo);
        }
        this.chineseName = book.getChineseName();
        this.englishName = book.getEnglishName();
        this.language = LanguageEnum.getByCode(book.getLanguage());
        this.businessType = BusinessTypeEnum.getByCode(book.getBusinessType());
        this.series = new SeriesDTO(book.getSeries());
        this.course = book.getCourse();
        this.courseNature = CourseNatureEnum.getByCode(book.getCourseNature());
        this.applicableMajor = book.getApplicableMajor();
        this.applicableGrade = book.getApplicableGrade();
        this.contactPhone = book.getContactPhone();
        this.contactEmail = book.getContactEmail();
        this.pcCoverUrl = book.getPcCoverUrl();
        this.appHorizontalCoverUrl = book.getAppHorizontalCoverUrl();
        this.appVerticalCoverUrl = book.getAppVerticalCoverUrl();
        this.createTime = book.getCreateTime().getTime();
        this.description = book.getDescription();
        this.lightColor = book.getLightColor();
        this.darkColor = book.getDarkColor();
        this.digitalFlag = book.getDigitalFlag();
        this.bookVersion=book.getBookVersion();
    }
    public BookDetailDTO fillCollaborators(List<ResourceUser> collaborators) {
        if (collaborators == null) {
            return this;
        }
        this.collaborators = collaborators.stream().map(BookUserDTO::new).toList();
        return this;
    }

    public BookDetailDTO fillPublishTimeAndFlag(Map<String, Long> publishTimeMap) {
        this.setPublishFlag(publishTimeMap.containsKey(this.id));
        this.setPublishTime(publishTimeMap.get(this.id));
        return this;
    }
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public BookUserDTO getEditor() {
        return editor;
    }

    public void setEditor(BookUserDTO editor) {
        this.editor = editor;
    }

    public String getChineseName() {
        return chineseName;
    }

    public void setChineseName(String chineseName) {
        this.chineseName = chineseName;
    }

    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }

    public LanguageEnum getLanguage() {
        return language;
    }

    public void setLanguage(LanguageEnum language) {
        this.language = language;
    }

    public BusinessTypeEnum getBusinessType() {
        return businessType;
    }

    public void setBusinessType(BusinessTypeEnum businessType) {
        this.businessType = businessType;
    }

    public SeriesDTO getSeries() {
        return series;
    }

    public void setSeries(SeriesDTO series) {
        this.series = series;
    }

    public String getCourse() {
        return course;
    }

    public void setCourse(String course) {
        this.course = course;
    }

    public CourseNatureEnum getCourseNature() {
        return courseNature;
    }

    public void setCourseNature(CourseNatureEnum courseNature) {
        this.courseNature = courseNature;
    }

    public String getApplicableMajor() {
        return applicableMajor;
    }

    public void setApplicableMajor(String applicableMajor) {
        this.applicableMajor = applicableMajor;
    }

    public String getApplicableGrade() {
        return applicableGrade;
    }

    public void setApplicableGrade(String applicableGrade) {
        this.applicableGrade = applicableGrade;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getPcCoverUrl() {
        return pcCoverUrl;
    }

    public void setPcCoverUrl(String pcCoverUrl) {
        this.pcCoverUrl = pcCoverUrl;
    }

    public String getAppHorizontalCoverUrl() {
        return appHorizontalCoverUrl;
    }

    public void setAppHorizontalCoverUrl(String appHorizontalCoverUrl) {
        this.appHorizontalCoverUrl = appHorizontalCoverUrl;
    }

    public String getAppVerticalCoverUrl() {
        return appVerticalCoverUrl;
    }

    public void setAppVerticalCoverUrl(String appVerticalCoverUrl) {
        this.appVerticalCoverUrl = appVerticalCoverUrl;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public BookUserDTO getCreateBy() {
        return createBy;
    }

    public void setCreateBy(BookUserDTO createBy) {
        this.createBy = createBy;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }


    public String getLightColor() {
        return lightColor;
    }

    public void setLightColor(String lightColor) {
        this.lightColor = lightColor;
    }

    public String getDarkColor() {
        return darkColor;
    }

    public void setDarkColor(String darkColor) {
        this.darkColor = darkColor;
    }

    public List<BookUserDTO> getCollaborators() {
        return collaborators;
    }

    public void setCollaborators(List<BookUserDTO> collaborators) {
        this.collaborators = collaborators;
    }

    public Boolean getDigitalFlag() {
        return digitalFlag;
    }

    public void setDigitalFlag(Boolean digitalFlag) {
        this.digitalFlag = digitalFlag;
    }

    public Long getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Long publishTime) {
        this.publishTime = publishTime;
    }

    public boolean isPublishFlag() {
        return publishFlag;
    }

    public void setPublishFlag(boolean publishFlag) {
        this.publishFlag = publishFlag;
    }

    public BookVersion getBookVersion() {
        return bookVersion;
    }

    public void setBookVersion(BookVersion bookVersion) {
        this.bookVersion = bookVersion;
    }
}
