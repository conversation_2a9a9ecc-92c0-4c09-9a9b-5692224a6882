package com.unipus.digitalbook.model.dto;

import com.unipus.digitalbook.model.entity.Series;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 教材系列
 */
@Schema(description = "教材系列")
public class SeriesDTO implements Serializable {
    @Schema(description = "教材系列id")
    private Long id;

    @Schema(description = "教材系列名称")
    private String name;

    public SeriesDTO(Series series) {
        if (series != null) {
            this.id = series.getId();
            this.name = series.getName();
        }
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
