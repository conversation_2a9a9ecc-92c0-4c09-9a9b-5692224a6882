package com.unipus.digitalbook.model.dto.user;

import com.unipus.digitalbook.model.entity.user.UserImportResult;
import com.unipus.digitalbook.model.enums.UserImportStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "导入结果数据传输对象")
public class ImportResultDTO implements Serializable {

    @Schema(description = "总记录数", example = "100")
    private Integer total;

    @Schema(description = "成功记录数", example = "95")
    private Integer successNum;

    @Schema(description = "失败记录数", example = "5")
    private Integer failNum;

    @Schema(description = "失败 Excel 文件 URL", example = "https://example.com/fail_excel.xlsx")
    private String failExcelUrl;

    @Schema(description = "状态：1-处理中 2-处理完成", example = "1")
    private Integer taskStatus;

    @Schema(description = "进度描述", example = "正在处理...")
    private String progress;

    public ImportResultDTO() {
    }

    public ImportResultDTO(UserImportResult userImportResult) {
        if (userImportResult == null) {
            return;
        }
        this.total = userImportResult.getTotal();
        this.successNum = userImportResult.getSuccessNum();
        this.failNum = userImportResult.getFailNum();
        this.failExcelUrl = userImportResult.getFailExcelUrl();
        this.taskStatus = userImportResult.getTaskStatus();
        // 添加任务状态描述
        if (UserImportStatusEnum.PROCESSING.getCode().equals(userImportResult.getTaskStatus())) {
            this.progress = UserImportStatusEnum.PROCESSING.getDesc();
        } else if (UserImportStatusEnum.COMPLETED.getCode().equals(userImportResult.getTaskStatus())) {
            if (this.failNum == 0) {
                this.progress = String.format("共计导入%s条数据，成功%s条，失败0条", this.total, this.total);
            } else {
                this.progress = String.format("共计导入%s条数据，成功%s条，失败%s条，下载导入失败文件可查看失败详情", this.total, this.successNum, this.failNum);
            }
        }
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getSuccessNum() {
        return successNum;
    }

    public void setSuccessNum(Integer successNum) {
        this.successNum = successNum;
    }

    public Integer getFailNum() {
        return failNum;
    }

    public void setFailNum(Integer failNum) {
        this.failNum = failNum;
    }

    public String getFailExcelUrl() {
        return failExcelUrl;
    }

    public void setFailExcelUrl(String failExcelUrl) {
        this.failExcelUrl = failExcelUrl;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getProgress() {
        return progress;
    }

    public void setProgress(String progress) {
        this.progress = progress;
    }
}
