package com.unipus.digitalbook.model.dto.cos;

import com.qcloud.cos.model.ciModel.template.MediaTemplateObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 音频转码模版DTO
 *
 * <AUTHOR>
 * @date 2025/01/14
 */
@Data
@Schema(description = "音频转码模版DTO")
public class MediaTranscodeTemplateDTO implements Serializable {

    @Schema(description = "模版ID")
    private String templateId;

    @Schema(description = "模版名称")
    private String templateName;

    public MediaTranscodeTemplateDTO() {
    }

    public MediaTranscodeTemplateDTO(String templateId, String templateName) {
        this.templateId = templateId;
        this.templateName = templateName;
    }

    public MediaTranscodeTemplateDTO(MediaTemplateObject mediaTemplateObject) {
        if (mediaTemplateObject != null) {
            this.templateId = mediaTemplateObject.getTemplateId();
            this.templateName = mediaTemplateObject.getName();
        }
    }
}
