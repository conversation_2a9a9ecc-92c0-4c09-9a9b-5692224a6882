package com.unipus.digitalbook.model.dto.feishu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 飞书消息基础模型
 */
@Data
public class FeishuMessage {
    
    /**
     * 消息类型
     */
    @JsonProperty("msg_type")
    private String msgType;
    
    /**
     * 消息内容 - 用于文本和富文本消息
     */
    private Object content;
    
    /**
     * 卡片内容 - 用于卡片消息
     */
    private Object card;
    
    /**
     * 消息签名
     */
    private String sign;
    
    /**
     * 时间戳
     */
    private Long timestamp;
} 