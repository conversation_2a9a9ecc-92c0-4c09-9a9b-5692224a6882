package com.unipus.digitalbook.model.dto.menu;

import com.unipus.digitalbook.model.entity.menu.Menu;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

@Schema(description = "菜单列表")
public class MenuListDTO implements Serializable {

    @Schema(description = "菜单列表")
    private List<MenuDTO> list;

    public MenuListDTO() {
    }

    public MenuListDTO(List<Menu> list) {
        this.list = list.stream().map(MenuDTO::new).toList();
    }

    public List<MenuDTO> getList() {
        return list;
    }

    public void setList(List<MenuDTO> list) {
        this.list = list;
    }
}
