package com.unipus.digitalbook.model.dto.book;

import com.unipus.digitalbook.model.entity.book.Book;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 教材封面与简介实体
 */
@Schema(description = "教材封面与简介")
public class BookCoverIntroDTO implements Serializable {
    @Schema(description = "当前实体的ID，与versionId一致")
    private Long id;

    @Schema(description = "关联教材ID", example = "B001")
    private String bookId;

    @Schema(description = "教材详细介绍", example = "这是一本介绍计算机科学基础的教材，适合初学者学习。")
    private String description;

    @Schema(description = "PC端封面图片地址", example = "https://example.com/pc-cover.jpg")
    private String pcCoverUrl;

    @Schema(description = "APP横版封面图片地址", example = "https://example.com/app-horizontal-cover.jpg")
    private String appHorizontalCoverUrl;

    @Schema(description = "APP竖版封面图片地址", example = "https://example.com/app-vertical-cover.jpg")
    private String appVerticalCoverUrl;

    @Schema(description = "版本号")
    private String versionNumber;

    @Schema(description = "版本id")
    private Long versionId;

    public BookCoverIntroDTO() {
    }

    public BookCoverIntroDTO(Book book) {
        if (book == null) {
            return;
        }
        this.bookId = book.getId();
        this.description = book.getDescription();
        this.pcCoverUrl = book.getPcCoverUrl();
        this.appHorizontalCoverUrl = book.getAppHorizontalCoverUrl();
        this.appVerticalCoverUrl = book.getAppVerticalCoverUrl();
        this.versionNumber = book.getBookIntro().getVersionNumber();
        this.versionId = book.getBookIntro().getId();
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPcCoverUrl() {
        return pcCoverUrl;
    }

    public void setPcCoverUrl(String pcCoverUrl) {
        this.pcCoverUrl = pcCoverUrl;
    }

    public String getAppHorizontalCoverUrl() {
        return appHorizontalCoverUrl;
    }

    public void setAppHorizontalCoverUrl(String appHorizontalCoverUrl) {
        this.appHorizontalCoverUrl = appHorizontalCoverUrl;
    }

    public String getAppVerticalCoverUrl() {
        return appVerticalCoverUrl;
    }

    public void setAppVerticalCoverUrl(String appVerticalCoverUrl) {
        this.appVerticalCoverUrl = appVerticalCoverUrl;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Long getVersionId() {
        return versionId;
    }

    public void setVersionId(Long versionId) {
        this.versionId = versionId;
    }
}
