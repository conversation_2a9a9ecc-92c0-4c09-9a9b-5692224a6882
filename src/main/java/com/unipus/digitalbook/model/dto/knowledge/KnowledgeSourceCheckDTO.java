package com.unipus.digitalbook.model.dto.knowledge;

import com.unipus.digitalbook.model.params.Params;
import com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceCompleteInfoPO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年06月05日 18:10
 */
@Schema(description = "知识点资源检查返回值")
@Data
public class KnowledgeSourceCheckDTO implements Params {

    private Boolean effectiveStatus = true;

    private List<BookKnowledgeResourceCompleteInfoPO> infoList;

    @Override
    public void valid() {
    }

}
