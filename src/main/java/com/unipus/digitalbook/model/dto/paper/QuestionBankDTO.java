package com.unipus.digitalbook.model.dto.paper;

import com.unipus.digitalbook.common.utils.DateUtil;
import com.unipus.digitalbook.model.entity.paper.QuestionBank;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 题库DTO
 */
@Schema(description = "题库DTO")
public class QuestionBankDTO implements Serializable {

    @Schema(description = "题库主键ID")
    private Long id;
    @Schema(description = "试卷ID")
    private String paperId;
    @Schema(description = "题库ID")
    private String bankId;
    @Schema(description = "题库名称")
    private String bankName;
    @Schema(description = "题目数量")
    private Integer questionCount;
    @Schema(description = "每题分数")
    private Integer questionScore;
    @Schema(description = "每轮随机抽取题目数")
    private Integer questionsPerRound;
    @Schema(description = "创建时间")
    private String createTime;
    @Schema(description = "创建人")
    private String createBy;
    @Schema(description = "题目列表")
    private PaperQuestionListDTO questionListDTO;

    public QuestionBankDTO(QuestionBank questionBank) {
        this.id = questionBank.getId();
        this.paperId = questionBank.getPaperId();
        this.bankId = questionBank.getBankId();
        this.bankName = questionBank.getBankName();
        this.questionCount = questionBank.getQuestionCount();
        this.questionScore = questionBank.getQuestionScore();
        this.questionsPerRound = questionBank.getQuestionsPerRound();
        this.createTime = DateUtil.formatDateTimeMinutesWithYearOrNot(questionBank.getCreateTime());
        this.createBy = questionBank.getCreatorName();
    }

    public QuestionBankDTO(QuestionBank questionBank, PaperQuestionListDTO questionListDTO) {
        this(questionBank);
        this.questionListDTO = questionListDTO;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getBankId() {
        return bankId;
    }

    public void setBankId(String bankId) {
        this.bankId = bankId;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public Integer getQuestionCount() {
        return questionCount;
    }

    public void setQuestionCount(Integer questionCount) {
        this.questionCount = questionCount;
    }

    public Integer getQuestionScore() {
        return questionScore;
    }

    public void setQuestionScore(Integer questionScore) {
        this.questionScore = questionScore;
    }

    public Integer getQuestionsPerRound() {
        return questionsPerRound;
    }

    public void setQuestionsPerRound(Integer questionsPerRound) {
        this.questionsPerRound = questionsPerRound;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public PaperQuestionListDTO getQuestionListDTO() {
        return questionListDTO;
    }

    public void setQuestionListDTO(PaperQuestionListDTO questionListDTO) {
        this.questionListDTO = questionListDTO;
    }
}
