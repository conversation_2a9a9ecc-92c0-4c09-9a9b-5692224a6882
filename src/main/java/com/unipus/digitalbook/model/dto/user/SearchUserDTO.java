package com.unipus.digitalbook.model.dto.user;

import com.unipus.digitalbook.model.entity.user.SearchUser;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

@Schema(description = "用户信息数据传输对象")
public class SearchUserDTO implements Serializable {

    @Schema(description = "用户ID", example = "123456")
    private Long id;

    @Schema(description = "用户姓名", example = "张三")
    private String name;

    @Schema(description = "手机号", example = "13800138000")
    private String cellPhone;

    @Schema(description = "所属机构")
    private String org;

    @Schema(description = "所属机构ID")
    private Long orgId;

    @Schema(description = "角色列表")
    private List<String> roleList;

    @Schema(description = "状态（0-未激活 1-已激活）", example = "true")
    private Integer status;

    @Schema(description = "激活时间", example = "1630000000000")
    private Long activeTime;

    @Schema(description = "创建时间", example = "1630000000000")
    private Long createTime;

    public SearchUserDTO() {
    }

    public SearchUserDTO(SearchUser searchUserInfo) {
        if (searchUserInfo == null) {
            return;
        }
        this.id = searchUserInfo.getId();
        this.name = searchUserInfo.getName();
        this.cellPhone = searchUserInfo.getCellPhone();
        this.org = searchUserInfo.getOrg();
        this.orgId = searchUserInfo.getOrgId();
        this.roleList = searchUserInfo.getRoleList();
        this.status = searchUserInfo.getStatus();
        this.activeTime = searchUserInfo.getActiveTime() != null ? searchUserInfo.getActiveTime().getTime() : null;
        this.createTime = searchUserInfo.getCreateTime() != null ? searchUserInfo.getCreateTime().getTime() : null;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCellPhone() {
        return cellPhone;
    }

    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }

    public String getOrg() {
        return org;
    }

    public void setOrg(String org) {
        this.org = org;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public List<String> getRoleList() {
        return roleList;
    }

    public void setRoleList(List<String> roleList) {
        this.roleList = roleList;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getActiveTime() {
        return activeTime;
    }

    public void setActiveTime(Long activeTime) {
        this.activeTime = activeTime;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "SearchUserInfoDTO{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", cellPhone='" + cellPhone + '\'' +
                ", org=" + org +
                ", orgId=" + orgId +
                ", roleList=" + roleList +
                ", status=" + status +
                ", activeTime=" + activeTime +
                ", createTime=" + createTime +
                '}';
    }
}
