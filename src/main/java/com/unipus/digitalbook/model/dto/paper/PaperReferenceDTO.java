package com.unipus.digitalbook.model.dto.paper;

import com.unipus.digitalbook.common.utils.DateUtil;
import com.unipus.digitalbook.model.entity.paper.PaperReference;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "用户试卷作答记录DTO")
public class PaperReferenceDTO implements Serializable {

    @Schema(description = "试卷引用主键ID")
    private Long id;
    @Schema(description = "试卷ID")
    private String paperId;
    @Schema(description = "教材ID")
    private String bookId;
    @Schema(description = "章节ID")
    private String chapterId;
    @Schema(description = "试卷在章节里面的插入位置")
    private String position;
    @Schema(description = "创建时间:yyyy-MM-dd HH:mm")
    private String createTime;
    @Schema(description = "试卷名称")
    private String paperName;
    @Schema(description = "试卷主键ID")
    private Long paperPrimaryId;

    public PaperReferenceDTO(PaperReference paperReference){
        this.id = paperReference.getId();
        this.paperId = paperReference.getPaperId();
        this.bookId = paperReference.getBookId();
        this.chapterId = paperReference.getChapterId();
        this.position = paperReference.getPosition();
        this.createTime = DateUtil.dateTimeFormatMinutes(paperReference.getCreateTime());
        this.paperName = paperReference.getPaperName();
        this.paperPrimaryId = paperReference.getPaperPrimaryId();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getPaperName() {
        return paperName;
    }

    public void setPaperName(String paperName) {
        this.paperName = paperName;
    }

    public Long getPaperPrimaryId() {
        return paperPrimaryId;
    }

    public void setPaperPrimaryId(Long paperPrimaryId) {
        this.paperPrimaryId = paperPrimaryId;
    }
}
