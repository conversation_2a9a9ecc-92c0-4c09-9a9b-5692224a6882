package com.unipus.digitalbook.model.dto.cos;

import com.unipus.digitalbook.model.entity.cos.COSMediaTranscodeJob;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 音视频转码任务DTO
 *
 * <AUTHOR>
 * @date 2024/12/11 15:58
 */
@Data
@Schema(description = "音视频转码任务DTO")
public class COSMediaTranscodeJobDTO implements Serializable {

    @Schema(description = "任务ID")
    private String jobId;

    @Schema(description = "任务状态 Submitted：已提交，待执行、Running：执行中、Success：执行成功、Failed：执行失败、Pause：任务暂停，当暂停队列时，待执行的任务会变为暂停状态、Cancel：任务被取消执行")
    private String state;

    @Schema(description = "错误码")
    private String code;

    @Schema(description = "错误信息")
    private String message;

    @Schema(description = "输出文件URL")
    private String outputUrl;

    /**
     * 通过实体构造DTO
     */
    public COSMediaTranscodeJobDTO(COSMediaTranscodeJob entity) {
        if (entity == null) {
            return;
        }
        this.jobId = entity.getJobId();
        this.state = entity.getState();
        this.code = entity.getCode();
        this.message = entity.getMessage();
        this.outputUrl = entity.getOutputUrl();
    }
}
