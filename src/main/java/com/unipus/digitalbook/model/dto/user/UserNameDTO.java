package com.unipus.digitalbook.model.dto.user;

import com.unipus.digitalbook.model.entity.UserInfo;

import java.io.Serializable;

public class UserNameDTO implements Serializable {
    /**
     * 用户的名字
     */
    private String name;
    /**
     * 用户的id
     */
    private Long id;

    public UserNameDTO() {
    }

    public UserNameDTO(UserInfo info) {
        if (info != null) {
            this.name = info.getName();
            this.id = info.getId();
        }
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
