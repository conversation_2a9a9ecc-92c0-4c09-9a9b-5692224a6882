<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.CustomContentPOMapper">
  <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.content.CustomContentPO">
    <!--@mbg.generated-->
    <!--@Table custom_content-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_id" jdbcType="CHAR" property="bizId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="student_content" jdbcType="LONGVARCHAR" property="studentContent" />
    <result column="content_hash_code" jdbcType="VARCHAR" property="contentHashCode" />
    <result column="student_content_hash_code" jdbcType="VARCHAR" property="studentContentHashCode" />
    <result column="header_img" jdbcType="VARCHAR" property="headerImg" />
    <result column="catalog" jdbcType="LONGVARCHAR" property="catalog" />
    <result column="total_struct" jdbcType="LONGVARCHAR" property="totalStruct"  typeHandler="com.unipus.digitalbook.conf.mybatis.type.handler.CustomContentNodePOListTypeHandler"/>
    <result column="resource" jdbcType="LONGVARCHAR" property="resource" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="enable" jdbcType="BIT" property="enable" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, biz_id, `type`, `status`, `name`, content, student_content, content_hash_code,
    student_content_hash_code, header_img, `catalog`, total_struct, `resource`, tenant_id,
    create_time, update_time, create_by, update_by, `enable`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from custom_content
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.content.CustomContentPO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into custom_content
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizId != null and bizId != ''">
        biz_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="name != null and name != ''">
        `name`,
      </if>
      <if test="content != null and content != ''">
        content,
      </if>
      <if test="studentContent != null and studentContent != ''">
        student_content,
      </if>
      <if test="contentHashCode != null and contentHashCode != ''">
        content_hash_code,
      </if>
      <if test="studentContentHashCode != null and studentContentHashCode != ''">
        student_content_hash_code,
      </if>
      <if test="headerImg != null and headerImg != ''">
        header_img,
      </if>
      <if test="catalog != null and catalog != ''">
        `catalog`,
      </if>
      <if test="totalStruct != null">
        total_struct,
      </if>
      <if test="resource != null and resource != ''">
        `resource`,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizId != null and bizId != ''">
        #{bizId,jdbcType=CHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="name != null and name != ''">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="content != null and content != ''">
        #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="studentContent != null and studentContent != ''">
        #{studentContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="contentHashCode != null and contentHashCode != ''">
        #{contentHashCode,jdbcType=VARCHAR},
      </if>
      <if test="studentContentHashCode != null and studentContentHashCode != ''">
        #{studentContentHashCode,jdbcType=VARCHAR},
      </if>
      <if test="headerImg != null and headerImg != ''">
        #{headerImg,jdbcType=VARCHAR},
      </if>
      <if test="catalog != null and catalog != ''">
        #{catalog,jdbcType=LONGVARCHAR},
      </if>
      <if test="totalStruct != null">
        #{totalStruct,typeHandler=com.unipus.digitalbook.conf.mybatis.type.handler.CustomContentNodePOListTypeHandler},
      </if>
      <if test="resource != null and resource != ''">
        #{resource,jdbcType=LONGVARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.content.CustomContentPO">
    <!--@mbg.generated-->
    update custom_content
    <set>
      <if test="bizId != null and bizId != ''">
        biz_id = #{bizId,jdbcType=CHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="name != null and name != ''">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="content != null and content != ''">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="studentContent != null and studentContent != ''">
        student_content = #{studentContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="contentHashCode != null and contentHashCode != ''">
        content_hash_code = #{contentHashCode,jdbcType=VARCHAR},
      </if>
      <if test="studentContentHashCode != null and studentContentHashCode != ''">
        student_content_hash_code = #{studentContentHashCode,jdbcType=VARCHAR},
      </if>
      <if test="headerImg != null and headerImg != ''">
        header_img = #{headerImg,jdbcType=VARCHAR},
      </if>
      <if test="catalog != null and catalog != ''">
        `catalog` = #{catalog,jdbcType=LONGVARCHAR},
      </if>
      <if test="totalStruct != null">
        total_struct = #{totalStruct,typeHandler=com.unipus.digitalbook.conf.mybatis.type.handler.CustomContentNodePOListTypeHandler},
      </if>
      <if test="resource != null and resource != ''">
        `resource` = #{resource,jdbcType=LONGVARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectEditingByBizId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from custom_content
    where biz_id = #{bizId,jdbcType=CHAR} and tenant_id = #{tenantId,jdbcType=BIGINT} and `status` = 0 and `enable` = 1
  </select>

  <select id="selectEditingByBizIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from custom_content
    where biz_id in
    <foreach close=")" collection="bizIds" item="bizId" open="(" separator=", ">
      #{bizId,jdbcType=CHAR}
    </foreach>
    and tenant_id = #{tenantId,jdbcType=BIGINT}
    and `status` = 0
    and `enable` = 1
  </select>

  <select id="selectPublishedByBizId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from custom_content
    where biz_id = #{bizId,jdbcType=CHAR} and tenant_id = #{tenantId,jdbcType=BIGINT} and `status` = 2 and `enable` = 1
  </select>

  <select id="selectPublishedByBizIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from custom_content
    where biz_id in
    <foreach close=")" collection="bizIds" item="bizId" open="(" separator=", ">
      #{bizId,jdbcType=CHAR}
    </foreach>
    and tenant_id = #{tenantId,jdbcType=BIGINT}
    and `status` = 2
    and `enable` = 1
  </select>

  <select id="selectByBizIdsAndStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from custom_content
    where biz_id in
    <foreach close=")" collection="bizIds" item="bizId" open="(" separator=", ">
      #{bizId,jdbcType=CHAR}
    </foreach>
    and tenant_id = #{tenantId,jdbcType=BIGINT}
    and `status` = #{status,jdbcType=INTEGER}
    and `enable` = 1
  </select>

  <select id="selectContentNodeByBizId" resultMap="BaseResultMap">
    select total_struct from custom_content where biz_id = #{bizId,jdbcType=CHAR} and `status` = 1 and `enable` = 1
  </select>

  <delete id="deleteEditingByBizIds">
    delete from custom_content
    where biz_id in
    <foreach close=")" collection="bizIds" item="bizId" open="(" separator=", ">
        #{bizId,jdbcType=CHAR}
    </foreach>
    and tenant_id = #{tenantId,jdbcType=BIGINT}
    and `status` = 0
    and `enable` = 1
  </delete>

  <delete id="deletePublishedByBizIds">
    delete from custom_content
    where biz_id in
    <foreach close=")" collection="bizIds" item="bizId" open="(" separator=", ">
      #{bizId,jdbcType=CHAR}
    </foreach>
    and tenant_id = #{tenantId,jdbcType=BIGINT}
    and `status` = 2
    and `enable` = 1
  </delete>

  <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    insert into custom_content (
      biz_id, `type`, `status`, `name`, content, student_content, content_hash_code, student_content_hash_code, header_img,
      `catalog`, total_struct, `resource`, tenant_id, create_time, update_time,
      create_by, update_by
    ) values
    <foreach collection="records" item="record" separator=",">
      (
        #{record.bizId,jdbcType=CHAR},
        #{record.type,jdbcType=INTEGER},
        #{record.status,jdbcType=INTEGER},
        #{record.name,jdbcType=VARCHAR},
        #{record.content,jdbcType=LONGVARCHAR},
        #{record.studentContent,jdbcType=LONGVARCHAR},
        #{record.contentHashCode,jdbcType=VARCHAR},
        #{record.studentContentHashCode,jdbcType=VARCHAR},
        #{record.headerImg,jdbcType=VARCHAR},
        #{record.catalog,jdbcType=LONGVARCHAR},
        #{record.totalStruct,typeHandler=com.unipus.digitalbook.conf.mybatis.type.handler.CustomContentNodePOListTypeHandler},
        #{record.resource,jdbcType=LONGVARCHAR},
        #{record.tenantId,jdbcType=BIGINT},
        #{record.createTime,jdbcType=TIMESTAMP},
        #{record.updateTime,jdbcType=TIMESTAMP},
        #{record.createBy,jdbcType=BIGINT},
        #{record.updateBy,jdbcType=BIGINT}
      )
    </foreach>
  </insert>

  <select id="selectEditingCatalogByBizIds" resultMap="BaseResultMap">
    select biz_id, `name`, `catalog`, `resource`
    from custom_content
    where biz_id in
    <foreach close=")" collection="bizIds" item="bizId" open="(" separator=", ">
      #{bizId,jdbcType=CHAR}
    </foreach>
    and tenant_id = #{tenantId,jdbcType=BIGINT}
    and `status` = 0
    and `enable` = 1
  </select>

  <select id="selectPublishedCatalogByBizIds" resultMap="BaseResultMap">
    select biz_id, `name`, `catalog`, `resource`
    from custom_content
    where biz_id in
    <foreach close=")" collection="bizIds" item="bizId" open="(" separator=", ">
      #{bizId,jdbcType=CHAR}
    </foreach>
    and tenant_id = #{tenantId,jdbcType=BIGINT}
    and `status` = 2
    and `enable` = 1
  </select>
</mapper>