```mermaid
erDiagram
    Textbook ||--o{ Chapter: contains
    Textbook ||--|| TextbookIntro: has
    Chapter ||--o{ ChapterVersion: has

    Textbook {
        uuid id PK "教材ID"
        string chinese_name "教材中文名称"
        string english_name "教材英文名称"
        enum language "语种：中文/English"
        string business_type "教材业务类型"
        string series "教程系列"
        string course "对应课程"
        string course_nature "课程性质"
        string applicable_major "适用专业"
        string applicable_grade "适用年级"
        string contact_phone "联系电话"
        string contact_email "联系邮箱"
        string pc_cover_url "PC端封面图片地址"
        string app_horizontal_cover_url "APP横版封面图片地址"
        string app_vertical_cover_url "APP竖版封面图片地址"
        datetime create_time "创建时间"
        datetime update_time "最后更新时间"
        bigint create_by "创建者ID"
        bigint update_by "最后更新者ID"
        bit enable "是否有效 0-无效 1-有效"
    }

    TextbookIntro {
        uuid id PK "简介ID"
        uuid textbook_id FK "关联教材ID"
        text description "教材详细介绍"
        datetime create_time "创建时间"
        datetime update_time "最后更新时间"
        bigint create_by "创建者ID"
        bigint update_by "最后更新者ID"
        bit enable "是否有效 0-无效 1-有效"
    }

    Chapter {
        uuid id PK "章节ID"
        uuid textbook_id FK "关联教材ID"
        int chapter_number "章节排序编号"
        string name "章节名称"
        datetime create_time "创建时间"
        datetime update_time "最后更新时间"
        bigint create_by "创建者ID"
        bigint update_by "最后更新者ID"
        bit enable "是否有效 0-无效 1-有效"
    }

    ChapterVersion {
        uuid id PK "版本ID"
        uuid chapter_id FK "关联章节ID"
        string version_number "版本号"
        text html_content "HTML格式内容"
        datetime create_time "创建时间"
        datetime update_time "最后更新时间"
        bigint create_by "创建者ID"
        bigint update_by "最后更新者ID"
        bit enable "是否有效 0-无效 1-有效"
    }
```
