# 自研数字教材文档

## 基本信息

- **描述**：符合OpenApi3规范的自研数字教材文档，自动生成
- **服务地址**：http://localhost:8889

## 用户相关功能

### 通过Excel，导入用户

**接口地址**：`POST /api/user/importByExcel`

**接口描述**：通过Excel，导入用户，返回任务id，后面根据任务ID查询进度和结果

#### 请求参数

| 参数名 | 参数位置 | 类型 | 是否必需 | 描述 |
|--------|----------|------|----------|------|
| orgId | query | string | 是 |  |

#### 请求体

```

对象 []：
  - file（必填）：
    字符串 (binary)

```


#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：响应数据
    字符串 [响应数据]
  - success：
    布尔值

```


---

### 新建用户

**接口地址**：`POST /api/user/AddUser`

**接口描述**：新建用户

#### 请求体

```

对象 []：
  - organizationId：所属机构ID
    整数 (int64) [所属机构ID] 示例: 12345
  - name：用户姓名
    字符串 [用户姓名] 示例: 张三
  - cellPhone：手机号
    字符串 [手机号] 示例: 13800138000
  - roleIds：角色ID列表
    数组 [角色ID列表] (
      整数 (int64) [角色ID列表]
    )

```


#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：响应数据
    布尔值 [响应数据]
  - success：
    布尔值

```


---

### 查询用户列表

**接口地址**：`GET /api/user/searchUserListForAmin`

**接口描述**：用户查询，根据手机号，组织，激活状态联合查询

#### 请求参数

| 参数名 | 参数位置 | 类型 | 是否必需 | 描述 |
|--------|----------|------|----------|------|
| phone | query | string | 是 |  |
| orgId | query | integer | 是 |  |
| active | query | boolean | 是 |  |

#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：
    对象 [用户列表]：
      - userList：
        数组 [] (
          对象 []：

        )

  - success：
    布尔值

```


---

### 导入用户的结果查询

**接口地址**：`GET /api/user/getImportResult`

**接口描述**：根据任务ID查询进度和结果

#### 请求参数

| 参数名 | 参数位置 | 类型 | 是否必需 | 描述 |
|--------|----------|------|----------|------|
| taskId | query | string | 是 |  |

#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：
    对象 [响应数据]：

  - success：
    布尔值

```


---

## 组织相关功能

### 编辑机构

**接口地址**：`POST /api/org/editOrg`

**接口描述**：超管的系统管理中，编辑机构，涉及到移动组织操作

#### 请求体

```

对象 []：
  - name：组织名称
    字符串 [组织名称] 示例: 一个组织名称
  - organizationType：组织类型
    整数 (int32) [组织类型]
  - parentId：上级组织id
    整数 (int64) [上级组织id] 示例: 12345

```


#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：响应数据
    布尔值 [响应数据]
  - success：
    布尔值

```


---

### 新建一级机构

**接口地址**：`POST /api/org/addTopOrg`

**接口描述**：超管的系统管理中，新建跟节点的机构

#### 请求体

```

对象 []：
  - name：组织名称
    字符串 [组织名称] 示例: 一个组织名称
  - organizationType：组织类型
    整数 (int32) [组织类型]
  - status：组织状态
    整数 (int32) [组织状态]

```


#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：响应数据
    布尔值 [响应数据]
  - success：
    布尔值

```


---

### 新建下级机构

**接口地址**：`POST /api/org/addSubOrg`

**接口描述**：超管的系统管理中，新建非一级机构

#### 请求体

```

对象 []：
  - name：组织名称
    字符串 [组织名称] 示例: 一个组织名称
  - organizationType：组织类型
    整数 (int32) [组织类型]
  - parentId：上级组织id
    整数 (int64) [上级组织id] 示例: 1234567

```


#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：响应数据
    布尔值 [响应数据]
  - success：
    布尔值

```


---

### 获取所有组织列表

**接口地址**：`GET /api/org/getAllOrganizationList`

**接口描述**：超管的系统管理中，获取所有的组织,以及其下的所有子组织

#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：
    对象 [响应数据]：
      - organizationList：
        数组 [] (
          对象 [组织对象]：
            - tenantId：租户ID
              整数 (int64) [租户ID] 示例: 1234567890
            - name：组织名称
              字符串 [组织名称] 示例: Example Organization
            - orgType：组织类型
              整数 (int32) [组织类型] 示例: 1
            - status：组织状态
              整数 (int32) [组织状态] 示例: 1
            - subOrganizationList：子组织列表
              数组 [子组织列表] (
                对象 [组织对象]：
                  - tenantId：租户ID
                    整数 (int64) [租户ID] 示例: 1234567890
                  - name：组织名称
                    字符串 [组织名称] 示例: Example Organization
                  - orgType：组织类型
                    整数 (int32) [组织类型] 示例: 1
                  - status：组织状态
                    整数 (int32) [组织状态] 示例: 1
                  - subOrganizationList：子组织列表
                    数组 [子组织列表] (
                      对象 [组织对象]：
                        - tenantId：租户ID
                          整数 (int64) [租户ID] 示例: 1234567890
                        - name：组织名称
                          字符串 [组织名称] 示例: Example Organization
                        - orgType：组织类型
                          整数 (int32) [组织类型] 示例: 1
                        - status：组织状态
                          整数 (int32) [组织状态] 示例: 1
                        - subOrganizationList：子组织列表
                          数组 [子组织列表] (
                            对象 [组织对象]：
                              - tenantId：租户ID
                                达到最大递归深度
                              - name：组织名称
                                达到最大递归深度
                              - orgType：组织类型
                                达到最大递归深度
                              - status：组织状态
                                达到最大递归深度
                              - subOrganizationList：子组织列表
                                达到最大递归深度

                          )

                    )

              )

        )

  - success：
    布尔值

```


---

## 菜单相关功能

### 修改菜单排序

**接口地址**：`POST /api/menu/sort`

**接口描述**：修改菜单排序

#### 请求参数

| 参数名 | 参数位置 | 类型 | 是否必需 | 描述 |
|--------|----------|------|----------|------|
| sortParams | query | array | 是 |  |

#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：响应数据
    布尔值 [响应数据]
  - success：
    布尔值

```


---

### 保存菜单

**接口地址**：`POST /api/menu/save`

**接口描述**：用户新增或者修改菜单

#### 请求参数

| 参数名 | 参数位置 | 类型 | 是否必需 | 描述 |
|--------|----------|------|----------|------|
| param | query | 未知 | 是 |  |

#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：响应数据
    布尔值 [响应数据]
  - success：
    布尔值

```


---

### 删除菜单

**接口地址**：`POST /api/menu/delete`

**接口描述**：删除菜单

#### 请求参数

| 参数名 | 参数位置 | 类型 | 是否必需 | 描述 |
|--------|----------|------|----------|------|
| id | query | integer | 是 |  |

#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：响应数据
    布尔值 [响应数据]
  - success：
    布尔值

```


---

### 获取用户菜单树

**接口地址**：`GET /api/menu/tree`

**接口描述**：获取当前登录用于有权限的菜单列表

#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：
    对象 [菜单列表]：

  - success：
    布尔值

```


---

### 获取菜单

**接口地址**：`GET /api/menu/info`

**接口描述**：根据菜单ID获取菜单信息

#### 请求参数

| 参数名 | 参数位置 | 类型 | 是否必需 | 描述 |
|--------|----------|------|----------|------|
| id | query | integer | 是 |  |

#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：
    对象 [菜单模型]：

  - success：
    布尔值

```


---

## 教材权限操作

### 移除阅读权限

**接口地址**：`POST /api/book/permission/remove/read`

**接口描述**：删除阅读权限

#### 请求参数

| 参数名 | 参数位置 | 类型 | 是否必需 | 描述 |
|--------|----------|------|----------|------|
| bookId | query | integer | 是 |  |
| userIds | query | array | 是 |  |

#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：响应数据
    布尔值 [响应数据]
  - success：
    布尔值

```


---

### 批量移除章节编辑权限

**接口地址**：`POST /api/book/permission/remove/edit`

**接口描述**：批量删除章节编辑权限

#### 请求参数

| 参数名 | 参数位置 | 类型 | 是否必需 | 描述 |
|--------|----------|------|----------|------|
| chapterIds | query | array | 是 |  |
| userIds | query | array | 是 |  |

#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：响应数据
    布尔值 [响应数据]
  - success：
    布尔值

```


---

### 分配阅读权限

**接口地址**：`POST /api/book/permission/assign/read`

**接口描述**：分配阅读权限

#### 请求参数

| 参数名 | 参数位置 | 类型 | 是否必需 | 描述 |
|--------|----------|------|----------|------|
| bookId | query | integer | 是 |  |
| userIds | query | array | 是 |  |

#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：响应数据
    布尔值 [响应数据]
  - success：
    布尔值

```


---

### 批量分配章节的编辑权限

**接口地址**：`POST /api/book/permission/assign/edit`

**接口描述**：批量分配编辑权限

#### 请求参数

| 参数名 | 参数位置 | 类型 | 是否必需 | 描述 |
|--------|----------|------|----------|------|
| chapterIds | query | array | 是 |  |
| userIds | query | array | 是 |  |

#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：响应数据
    布尔值 [响应数据]
  - success：
    布尔值

```


---

## 认证与权限管理模块

### 登录

**接口地址**：`POST /api/auth/validate`

**接口描述**：用户登录,构建用户权限数据，返回jwt

#### 请求体

```

对象 [认证参数类]：
  - serviceTicket：服务票据，用于访问特定服务的凭证
    字符串 [服务票据，用于访问特定服务的凭证] 示例: ST-123456
  - ticketGrantingTicket：授权票据，用于获取服务票据的凭证
    字符串 [授权票据，用于获取服务票据的凭证] 示例: TGT-123456

```


#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：响应数据
    对象 [响应数据]：

  - success：
    布尔值

```


---

### 获取全部权限

**接口地址**：`GET /api/auth/getAllResources`

**接口描述**：自动获取所有的controller下的方法

#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：
    对象 [响应数据]：
      - controllerResourceList：
        数组 [] (
          对象 [每一个控制器所包含的请求资源]：
            - controller：所属controller
              字符串 [所属controller]
            - controllerDesc：controller描述
              字符串 [controller描述]
            - resourceList：包含的请求链接
              数组 [包含的请求链接] (
                对象 [包含的请求链接]：
                  - methodName：方法名
                    字符串 [方法名]
                  - apiDesc：资源说明
                    字符串 [资源说明]
                  - apiUrl：资源地址
                    字符串 [资源地址]
                  - requestType：请求方式
                    字符串 [请求方式]

              )

        )

  - success：
    布尔值

```


---

## test-controller

### 未命名接口

**接口地址**：`GET /hello`

**接口描述**：无

#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

字符串
```


---

## 教材相关功能

### 获取用户最近打开的教材列表

**接口地址**：`GET /book/getRecentBook`

**接口描述**：该接口用于获取用户最近打开的教材列表，返回一个包含最近打开教材信息的列表

#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：
    对象 [最近打开书籍列表]：
      - recentBookList：最近书籍列表
        数组 [最近书籍列表] (
          对象 [最近书籍的数据传输对象]：
            - id：书籍的唯一标识符
              整数 (int64) [书籍的唯一标识符] 示例: 1234567890
            - coverUrl：教材封面URL
              字符串 [教材封面URL] 示例: http://example.com/cover.jpg
            - name：教材名称
              字符串 [教材名称] 示例: Java基础教程
            - editStatus：编辑状态
              字符串 [编辑状态] 示例: 已出版
            - editPermission：是否有编辑权限
              布尔值 [是否有编辑权限] 示例: True
            - lastEditedTime：最新编辑时间
              字符串 (date-time) [最新编辑时间]
            - authorAvatars：编者头像列表（创建者）
              数组 [编者头像列表（创建者）] (
                字符串 [编者头像列表（创建者）] 示例: ["http://example.com/avatar1.jpg","http://example.com/avatar2.jpg"]
              )
            - author：编者
              数组 [编者] (
                字符串 [编者] 示例: 李书易
              )
            - collaboratorAvatars：协作者头像列表
              数组 [协作者头像列表] (
                字符串 [协作者头像列表] 示例: ["http://example.com/collaborator1.jpg","http://example.com/collaborator2.jpg"]
              )
            - collaboratorList：协作者列表
              数组 [协作者列表] (
                字符串 [协作者列表]
              )

        )

  - success：
    布尔值

```


---

### 获取我的教材统计列表

**接口地址**：`GET /book/getMyBookStatistics`

**接口描述**：获取我的教材统计列表，包括状态及相对应的数量

#### 响应

**状态码**：200

**说明**：成功获取最近打开的教材列表

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：
    对象 [响应数据]：
      - statusStatisticList：
        数组 [] (
          对象 []：

        )

  - success：
    布尔值

```


**状态码**：500

**说明**：服务器内部错误

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：
    对象 [响应数据]：
      - statusStatisticList：
        数组 [] (
          对象 []：

        )

  - success：
    布尔值

```


---

## 角色相关功能

### 保存角色

**接口地址**：`GET /api/role/save`

**接口描述**：保存角色

#### 请求参数

| 参数名 | 参数位置 | 类型 | 是否必需 | 描述 |
|--------|----------|------|----------|------|
| roleDTO | query | 未知 | 是 |  |

#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：响应数据
    布尔值 [响应数据]
  - success：
    布尔值

```


---

### 获取角色列表

**接口地址**：`GET /api/role/list`

**接口描述**：获取角色列表

#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：
    对象 [角色列表模型]：

  - success：
    布尔值

```


---

### 获取角色

**接口地址**：`GET /api/role/info`

**接口描述**：根据角色ID获取角色信息

#### 请求参数

| 参数名 | 参数位置 | 类型 | 是否必需 | 描述 |
|--------|----------|------|----------|------|
| id | query | integer | 是 |  |

#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：
    对象 [角色模型]：

  - success：
    布尔值

```


---

### 删除角色

**接口地址**：`GET /api/role/delete`

**接口描述**：删除角色

#### 请求参数

| 参数名 | 参数位置 | 类型 | 是否必需 | 描述 |
|--------|----------|------|----------|------|
| id | query | integer | 是 |  |

#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：响应数据
    布尔值 [响应数据]
  - success：
    布尔值

```


---

### 分配API接口权限

**接口地址**：`GET /api/role/assign/resources`

**接口描述**：给角色分配API接口权限

#### 请求参数

| 参数名 | 参数位置 | 类型 | 是否必需 | 描述 |
|--------|----------|------|----------|------|
| assignResourceParam | query | 未知 | 是 |  |

#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：响应数据
    布尔值 [响应数据]
  - success：
    布尔值

```


---

### 分配菜单权限

**接口地址**：`GET /api/role/assign/menu`

**接口描述**：给角色分配菜单权限

#### 请求参数

| 参数名 | 参数位置 | 类型 | 是否必需 | 描述 |
|--------|----------|------|----------|------|
| assignMenuParams | query | 未知 | 是 |  |

#### 响应

**状态码**：200

**说明**：OK

**响应体**：

```

对象 [通用响应对象]：
  - code：状态码
    整数 (int32) [状态码]
  - message：响应消息
    字符串 [响应消息]
  - data：响应数据
    布尔值 [响应数据]
  - success：
    布尔值

```


---

